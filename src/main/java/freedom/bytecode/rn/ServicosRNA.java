package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.ServicosRNW;
import freedom.data.DataException;
import freedom.data.RowState;
import freedom.data.TableState;
import freedom.util.CrmServiceUtil;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmServiceRNA;

public class ServicosRNA extends ServicosRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final CrmServiceUtil util = new CrmServiceUtil();
    private final PkgCrmServiceRNA serv = new PkgCrmServiceRNA();

    private Boolean aplicDesc = true;

    public void carregaFiltros() throws DataException {

        tbServicosGrupo.close();
        tbServicosGrupo.clearFilters();
        tbServicosGrupo.clearParams();
        tbServicosGrupo.setOrderBy("DESCRICAO_GRUPO");
        tbServicosGrupo.open();

        carregaSubGrupo();

        tbServicosSetores.close();
        tbServicosSetores.clearFilters();
        tbServicosSetores.clearParams();
        tbServicosSetores.setOrderBy("DESCRICAO_SETOR");
        tbServicosSetores.open();

        tbClassificacaoServ.close();
        tbClassificacaoServ.clearFilters();
        tbClassificacaoServ.clearParams();
        tbClassificacaoServ.setOrderBy("DESCRICAO");
        tbClassificacaoServ.open();

        tbComboClassificacaoServ.close();
        tbComboClassificacaoServ.clearFilters();
        tbComboClassificacaoServ.clearParams();
        tbComboClassificacaoServ.open();
    }

    public void carregaSubGrupo() throws DataException {
        tbServicosSubGrupo.close();
        tbServicosSubGrupo.clearFilters();
        tbServicosSubGrupo.clearParams();
        tbServicosSubGrupo.setOrderBy("DESCRICAO_SUB_GRUPO");
        tbServicosSubGrupo.open();
    }

    public void carregaSubGrupoCadastro() throws DataException {
        tbServicosSubGrupoCadastro.close();
        tbServicosSubGrupoCadastro.clearFilters();
        tbServicosSubGrupoCadastro.clearParams();
        tbServicosSubGrupoCadastro.setOrderBy("DESCRICAO_SUB_GRUPO");
        tbServicosSubGrupoCadastro.open();
    }

    public Boolean pesqServicos(int codGrupo,
                                int codSubGrupo,
                                int codSetor,
                                int codClass,
                                String ativo,
                                String original,
                                String cobrar,
                                int numeroOS,
                                Boolean terceiros,
                                Boolean trocaOleo,
                                Boolean lubrificacao,
                                Boolean tempoZero,
                                Boolean lavagem,
                                Boolean temAdicionais,
                                Boolean temMarca,
                                Boolean tmoPorModelo,
                                Boolean nForca,
                                Boolean nPermitir,
                                Boolean terceirosRemessa,
                                Boolean passivelRemessa,
                                Boolean nReterPCC,
                                Boolean retemInss,
                                Boolean retemIRRF,
                                Boolean bmwIspa,
                                Boolean autFabrica,
                                String codServ,
                                String descServ,
                                Boolean nRetemPCC,
                                Boolean likeDescricao) throws DataException {

        tbConsultaServico.close();
        tbConsultaServico.clearFilters();
        tbConsultaServico.clearParams();

        tbConsultaServico.addParam("USUARIO", EmpresaUtil.getUserLogged());
        int countFilters = 0;

        if (codGrupo > 0) {
            tbConsultaServico.addFilter("GRUPO");
            tbConsultaServico.addParam("GRUPO", codGrupo);
            countFilters++;
        }
        if (codSubGrupo > 0) {
            tbConsultaServico.addFilter("SUB_GRUPO");
            tbConsultaServico.addParam("SUB_GRUPO", codSubGrupo);
            countFilters++;
        }
        if (codSetor > 0) {
            tbConsultaServico.addFilter("SETOR");
            tbConsultaServico.addParam("SETOR", codSetor);
            countFilters++;
        }

        if (!ativo.equals("")) {
            tbConsultaServico.addFilter("ATIVO");
            tbConsultaServico.addParam("ATIVO", ativo);
            countFilters++;
        }

        if (!original.equals("")) {
            tbConsultaServico.addFilter("ORIGINAL");
            tbConsultaServico.addParam("ORIGINAL", original);
            countFilters++;
        }

        if (!cobrar.equals("")) {
            tbConsultaServico.addFilter("COMO_COBRAR");
            tbConsultaServico.addParam("COMO_COBRAR", cobrar);
            countFilters++;
        }

        if (numeroOS > 0) {
            tbConsultaServico.addFilter("NUMERO_OS");
            tbConsultaServico.addParam("NUMERO_OS", numeroOS);
            countFilters++;
        }

        if (terceiros) {
            tbConsultaServico.addFilter("TERCEIROS");
            countFilters++;
        }

        if (trocaOleo) {
            tbConsultaServico.addFilter("TROCA_OLEO");
            countFilters++;
        }

        if (lubrificacao) {
            tbConsultaServico.addFilter("LUBRIFICACAO");
            countFilters++;
        }

        if (tempoZero) {
            tbConsultaServico.addFilter("TEMPO_PADRAO");
            countFilters++;
        }

        if (lavagem) {
            tbConsultaServico.addFilter("LAVAGEM");
            countFilters++;
        }

        if (temAdicionais) {
            tbConsultaServico.addFilter("ADICIONAIS");
            countFilters++;
        }

        if (temMarca) {
            tbConsultaServico.addFilter("MARCA");
            countFilters++;
        }

        if (tmoPorModelo) {
            tbConsultaServico.addFilter("N_TEM_MODELO");
            countFilters++;
        }

        if (nForca) {
            tbConsultaServico.addFilter("APONTAMENTO_FORCAR");
            countFilters++;
        }

        if (nPermitir) {
            tbConsultaServico.addFilter("APONTAMENTO_PERMITIR");
            countFilters++;
        }

        if (terceirosRemessa) {
            tbConsultaServico.addFilter("TERCEIRO_REMESSA");
            countFilters++;
        }

        if (passivelRemessa) {
            tbConsultaServico.addFilter("TEM_REMESSA");
            countFilters++;
        }

        if (nReterPCC) {
            tbConsultaServico.addFilter("NAO_RETEM_PCC");
            countFilters++;
        }

        if (retemInss) {
            tbConsultaServico.addFilter("RETEM_INSS");
            countFilters++;
        }

        if (retemIRRF) {
            tbConsultaServico.addFilter("RETEM_IRRF");
            countFilters++;
        }

        if (bmwIspa) {
            tbConsultaServico.addFilter("BNW_ISPA");
            countFilters++;
        }

        if (autFabrica) {
            tbConsultaServico.addFilter("AUTORIZACAO");
            countFilters++;
        }

        if (!codServ.equals("")) {
            tbConsultaServico.addFilter("COD_SERVICO");
            tbConsultaServico.addParam("COD_SERVICO", codServ);
            countFilters++;
        }

        if (!descServ.equals("")) {
            tbConsultaServico.addFilter("DESCRICAO_SERVICO");
            if (!likeDescricao) {
                tbConsultaServico.addParam("DESCRICAO_SERVICO", descServ + "%");
            } else {
                tbConsultaServico.addParam("DESCRICAO_SERVICO", "%" + descServ + "%");
            }
            countFilters++;
        }

        if (nRetemPCC) {
            tbConsultaServico.addFilter("NAO_RETEM_PCC");
            countFilters++;
        }

        if (countFilters < 2) {
            return false;
        } else {
            tbConsultaServico.open();
            preparaTelaCadastro(tbConsultaServico.getCOD_SERVICO().asString());
            return true;
        }

    }

    public void preparaTelaCadastro(String codServ) throws DataException {
        tbServicosGrupoCadastro.close();
        tbServicosGrupoCadastro.clearFilters();
        tbServicosGrupoCadastro.clearParams();
        tbServicosGrupoCadastro.setOrderBy("DESCRICAO_GRUPO");
        tbServicosGrupoCadastro.open();

        //carregaSubGrupoCadastro();
        tbServicosSubGrupoCadastro.close();
        tbServicosSubGrupoCadastro.clearFilters();
        tbServicosSubGrupoCadastro.clearParams();

        tbServicosSetoresCadastro.close();
        tbServicosSetoresCadastro.clearFilters();
        tbServicosSetoresCadastro.clearParams();
        tbServicosSetoresCadastro.setOrderBy("DESCRICAO_SETOR");
        tbServicosSetoresCadastro.open();

        tbClassificacaoServCadastro.close();
        tbClassificacaoServCadastro.clearFilters();
        tbClassificacaoServCadastro.clearParams();
        tbClassificacaoServCadastro.setOrderBy("DESCRICAO");
        tbClassificacaoServCadastro.open();

        tbReinfClassifEfd.close();
        tbReinfClassifEfd.clearFilters();
        tbReinfClassifEfd.clearParams();
        tbReinfClassifEfd.setOrderBy("DESCRICAO");
        tbReinfClassifEfd.open();

        tbEmpresas.close();
        tbEmpresas.clearFilters();
        tbEmpresas.clearParams();
        tbEmpresas.addFilter("STATUS");
        tbEmpresas.addParam("STATUS", "S");
        tbEmpresas.addFilter("COD_MATRIZ");
        tbEmpresas.setOrderBy("NOME");
        tbEmpresas.open();

        tbMarcas.close();
        tbMarcas.clearFilters();
        tbMarcas.clearParams();
        tbMarcas.setOrderBy("DESCRICAO_MARCA2");
        tbMarcas.open();

        tbServicosLc1162003.close();
        tbServicosLc1162003.clearFilters();
        tbServicosLc1162003.clearParams();
        tbServicosLc1162003.setOrderBy("DESC2");
        tbServicosLc1162003.open();

        if (!codServ.equals("")) {

            tbConcessionariaTipo.close();
            tbConcessionariaTipo.clearFilters();
            tbConcessionariaTipo.clearParams();
            tbConcessionariaTipo.addFilter("SEM_CRUZAMENTO");
            tbConcessionariaTipo.addParam("SEM_CRUZAMENTO", codServ);
            tbConcessionariaTipo.setOrderBy("DESCRICAO2");
            tbConcessionariaTipo.open();

            tbServicoMarca.close();
            tbServicoMarca.clearFilters();
            tbServicoMarca.clearParams();
            tbServicoMarca.addFilter("COD_SERVICO");
            tbServicoMarca.addParam("COD_SERVICO", codServ);
            tbServicoMarca.setOrderBy("DESC_CONCESSIONARIA_TIPO2");
            tbServicoMarca.open();

            tbServicos.close();
            tbServicos.clearFilters();
            tbServicos.clearParams();
            tbServicos.addFilter("COD_SERVICO");
            tbServicos.addParam("COD_SERVICO", codServ);
            tbServicos.open();
        } else {
            tbServicos.close();
            tbServicos.clearFilters();
            tbServicos.clearParams();

            tbServicoMarca.close();
            tbServicoMarca.clearFilters();
            tbServicoMarca.clearParams();

            tbConcessionariaTipo.close();
            tbConcessionariaTipo.clearFilters();
            tbConcessionariaTipo.clearParams();
            tbConcessionariaTipo.setOrderBy("DESCRICAO2");
            tbConcessionariaTipo.open();
        }

    }

    public void carregaProdutosCadastro(int codMarca) throws DataException {
        tbProdutos.close();
        tbProdutos.clearFilters();
        tbProdutos.clearParams();
        tbProdutos.addFilter("ATIVO");
        tbProdutos.addParam("ATIVO", "S");
        tbProdutos.addFilter("NOVO_USADO");
        tbProdutos.addParam("NOVO_USADO", "N");
        if (codMarca > 0) {
            tbProdutos.addFilter("COD_MARCA");
            tbProdutos.addParam("COD_MARCA", codMarca);
        }
        tbProdutos.setOrderBy("DESC_PRODUTO_INIT");
        tbProdutos.open();
    }

    public void salvar(String codServ) throws DataException {
        tbServicos.post();
//        if (!codServ.equals("")) {
//            adicionaCodServico(codServ, 0);
//        }
        util.salvar(scServicos);
        tbServicos.commitUpdates();
        tbServicosAdicionais.commitUpdates();
        tbServicosPrefeitura.commitUpdates();
        tbServicosRedBcIss.commitUpdates();
        tbTemposPadroes.commitUpdates();
        tbServicoMarca.commitUpdates();
    }

    public void carregaProdutosModelosCadastro(Integer codProduto, String codServ) throws DataException {
        tbProdutosModelos.close();
        tbProdutosModelos.clearFilters();
        tbProdutosModelos.clearParams();
        tbProdutosModelos.addFilter("ATIVO");
        tbProdutosModelos.addParam("ATIVO", "S");
        if (codProduto > 0) {
            tbProdutosModelos.addFilter("COD_PRODUTO");
            tbProdutosModelos.addParam("COD_PRODUTO", codProduto);
        } else {
            tbTemposPadroes.close();
            tbTemposPadroes.clearFilters();
            tbTemposPadroes.clearParams();
            tbTemposPadroes.addFilter("COD_SERVICO_UPPER");
            tbTemposPadroes.addParam("COD_SERVICO_UPPER", codServ);
            tbTemposPadroes.setOrderBy("MODELO");
            tbTemposPadroes.open();
        }
        tbProdutosModelos.setOrderBy("DESCRICAO_MODELO");
        tbProdutosModelos.open();

        if (!codProduto.equals("")) {
            tbTemposPadroes.close();
            tbTemposPadroes.clearFilters();
            tbTemposPadroes.clearParams();
            tbTemposPadroes.addFilter("COD_SERVICO_UPPER");
            tbTemposPadroes.addParam("COD_SERVICO_UPPER", codServ);
            tbTemposPadroes.addFilter("COD_PRODUTO");
            tbTemposPadroes.addParam("COD_PRODUTO", codProduto);
            tbTemposPadroes.setOrderBy("MODELO");
            tbTemposPadroes.open();
        }
    }

    public void cancelEdicaoTables() throws DataException {
        tbServicos.cancel();
        tbServicos.cancelUpdates();
        tbServicosAdicionais.cancel();
        tbServicosAdicionais.cancelUpdates();
        tbServicosPrefeitura.cancel();
        tbServicosPrefeitura.cancelUpdates();
        tbServicosRedBcIss.cancel();
        tbServicosRedBcIss.cancelUpdates();
        tbTemposPadroes.cancel();
        tbTemposPadroes.cancelUpdates();
    }

    public void adicionaCodServico(String codServ, Integer aba) throws DataException {
        if (aba == 2 || aba == 0) {
            tbServicosAdicionais.first();
            while (!tbServicosAdicionais.eof()) {
                //if (tbServicosAdicionais.getCOD_SERVICO().equals("") || tbServicosAdicionais.getCOD_SERVICO().equals(null) || forcarTroca) {
                tbServicosAdicionais.edit();
                tbServicosAdicionais.setCOD_SERVICO(codServ);
                tbServicosAdicionais.post();
                //}

                tbServicosAdicionais.next();
            }
        }

        if (aba == 1 || aba == 0) {
            tbServicosPrefeitura.first();
            while (!tbServicosPrefeitura.eof()) {
                //if (tbServicosPrefeitura.getCOD_SERVICO().equals("") || tbServicosPrefeitura.getCOD_SERVICO().equals(null) || forcarTroca) {
                tbServicosPrefeitura.edit();
                tbServicosPrefeitura.setCOD_SERVICO(codServ);
                tbServicosPrefeitura.post();
                //}

                tbServicosPrefeitura.next();
            }

            tbServicosRedBcIss.first();
            while (!tbServicosRedBcIss.eof()) {
                //if (tbServicosRedBcIss.getCOD_SERVICO().equals("") || tbServicosRedBcIss.getCOD_SERVICO().equals(null) || forcarTroca) {
                tbServicosRedBcIss.edit();
                tbServicosRedBcIss.setCOD_SERVICO(codServ);
                tbServicosRedBcIss.post();
                //}

                tbServicosRedBcIss.next();
            }
        }
        if (aba == 3 || aba == 0) {
            tbTemposPadroes.first();
            while (!tbTemposPadroes.eof()) {
                //if (tbTemposPadroes.getCOD_SERVICO().equals("") || tbTemposPadroes.getCOD_SERVICO().equals(null) || forcarTroca) {
                tbTemposPadroes.edit();
                tbTemposPadroes.setCOD_SERVICO(codServ);
                tbTemposPadroes.post();
                //}
                tbTemposPadroes.next();
            }
        }
        if (aba == 4 || aba == 0) {
            tbServicoMarca.first();
            while (!tbServicoMarca.eof()) {
                //if (tbServicoMarca.getCOD_SERVICO().equals("") || tbServicoMarca.getCOD_SERVICO().equals(null) || forcarTroca) {
                aplicDesc = false;
                tbServicoMarca.edit();
                tbServicoMarca.setCOD_SERVICO(codServ);
                tbServicoMarca.post();
                //}

                tbServicoMarca.next();
            }
        }

    }

    public void limpaCombos() {
        tbProdutos.close();
        tbProdutos.clearFilters();
        tbProdutos.clearParams();

        tbProdutosModelos.close();
        tbProdutosModelos.clearFilters();
        tbProdutosModelos.clearParams();
    }

    public Boolean validaChaveServPref(Integer codEmpresa, String codServ) throws DataException {
        tbServicosPrefeitura.first();
        while (!tbServicosPrefeitura.eof()) {
            if (tbServicosPrefeitura.getCOD_EMPRESA().asInteger() == codEmpresa && tbServicosPrefeitura.getCOD_SERVICO().asString() == codServ) {
                //throw new DataException("Não é permitido ter mais de um Código Prefeitura para o mesmo Serviço e Empresa.");
                return false;
            }
            tbServicosPrefeitura.next();
        }
        return true;
    }

    public Boolean validaChaveServRedBcIss(Integer codEmpresa, String codServ) throws DataException {
        tbServicosRedBcIss.first();
        while (!tbServicosRedBcIss.eof()) {
            if (tbServicosRedBcIss.getCOD_EMPRESA().asInteger() == codEmpresa && tbServicosRedBcIss.getCOD_SERVICO().asString() == codServ) {
                //throw new DataException("Não é permitido ter mais de uma Aliq. de Redução de Base para o mesmo Serviço e Empresa.");
                return false;
            }
            tbServicosRedBcIss.next();
        }
        return true;
    }

    public void carregaGridModelosCadastro(Integer codModelo, String codServ) throws DataException {
        if (!codServ.equals("")) {
            tbTemposPadroes.close();
            tbTemposPadroes.clearFilters();
            tbTemposPadroes.clearParams();
            tbTemposPadroes.addFilter("COD_SERVICO_UPPER");
            tbTemposPadroes.addParam("COD_SERVICO_UPPER", codServ);
            tbTemposPadroes.addFilter("COD_MODELO");
            tbTemposPadroes.addParam("COD_MODELO", codModelo);
            tbTemposPadroes.setOrderBy("MODELO");
            tbTemposPadroes.open();
        }
    }

    public void aplicaDescServMarca() throws DataException {
        if (aplicDesc) {
            tbServicoMarca.edit();
            tbServicoMarca.setDESC_CONCESSIONARIA_TIPO2(tbConcessionariaTipo.getDESCRICAO2().asString());
        } else {
            aplicDesc = true;
        }

    }

    public void deletarServicoFilhos() throws DataException {
        //tbServicosAdicionais.commitUpdates();
        tbServicosAdicionais.first();
        while (!tbServicosAdicionais.eof()) {
            tbServicosAdicionais.delete();
        }
        //tbServicosPrefeitura.commitUpdates();
        tbServicosPrefeitura.first();
        while (!tbServicosPrefeitura.eof()) {
            tbServicosPrefeitura.delete();
        }

        //tbServicosRedBcIss.commitUpdates();
        tbServicosRedBcIss.first();
        while (!tbServicosRedBcIss.eof()) {
            tbServicosRedBcIss.delete();
        }
        //tbTemposPadroes.commitUpdates();
        tbTemposPadroes.first();
        while (!tbTemposPadroes.eof()) {
            tbTemposPadroes.delete();
        }
        //tbServicoMarca.commitUpdates();
        tbServicoMarca.first();
        while (!tbServicoMarca.eof()) {
            tbServicoMarca.delete();
        }
        tbServicos.delete();

        salvar("");
    }

    public String atualizarTmoKit(String aCodservico,
                                  Double aTempopadrao) throws DataException {
        return serv.atualizarTmoKit(aCodservico, aTempopadrao);
    }

    public boolean existsServKit(String codServico) throws DataException {
        tbReclamacaoServicosEmpresa.close();
        tbReclamacaoServicosEmpresa.clearFilters();
        tbReclamacaoServicosEmpresa.clearParams();
        tbReclamacaoServicosEmpresa.addFilter("COD_SERVICO");
        tbReclamacaoServicosEmpresa.addParam("COD_SERVICO", codServico);
        return tbReclamacaoServicosEmpresa.sqlCompute("COUNT", "*").asInteger() > 0;
    }

    public void carregaAbaCadastro() throws DataException {
        if (TableState.MODIFYING.equals(tbServicos.getState()) && !tbServicos.getCOD_SERVICO().asString().equals(tbConsultaServico.getCOD_SERVICO().asString())) {
            preparaTelaCadastro(tbConsultaServico.getCOD_SERVICO().asString());
        }
    }

    public void carregaAbaValor() throws DataException {
        String codServ = tbConsultaServico.getCOD_SERVICO().asString();
        if (TableState.MODIFYING.equals(tbServicos.getState()) && !tbServicos.getCOD_SERVICO().asString().equals(codServ)) {
            carregaServicos(codServ);
        }
    }

    public void carregaImposto() throws DataException {
        String codServ = tbConsultaServico.getCOD_SERVICO().asString();
        if (!codServ.equals("")) {

            tbEmpresas.close();
            tbEmpresas.clearFilters();
            tbEmpresas.clearParams();
            tbEmpresas.addFilter("STATUS");
            tbEmpresas.addParam("STATUS", "S");
            tbEmpresas.addFilter("COD_MATRIZ");
            tbEmpresas.setOrderBy("NOME");
            tbEmpresas.open();

            tbServicosPrefeitura.close();
            tbServicosPrefeitura.clearFilters();
            tbServicosPrefeitura.clearParams();
            tbServicosPrefeitura.addFilter("COD_SERVICO");
            tbServicosPrefeitura.addParam("COD_SERVICO", codServ);
            tbServicosPrefeitura.open();

            tbServicosRedBcIss.close();
            tbServicosRedBcIss.clearFilters();
            tbServicosRedBcIss.clearParams();
            tbServicosRedBcIss.addFilter("COD_SERVICO");
            tbServicosRedBcIss.addParam("COD_SERVICO", codServ);
            tbServicosRedBcIss.open();

            if (TableState.MODIFYING.equals(tbServicos.getState()) && !tbServicos.getCOD_SERVICO().asString().equals(codServ)) {
                carregaServicos(codServ);
            }
        } else {
            tbServicos.close();
            tbServicos.clearFilters();
            tbServicos.clearParams();

            tbServicosRedBcIss.close();
            tbServicosRedBcIss.clearFilters();
            tbServicosRedBcIss.clearParams();

            tbServicosPrefeitura.close();
            tbServicosPrefeitura.clearFilters();
            tbServicosPrefeitura.clearParams();
        }

    }

    public void carregaAdicionais() throws DataException {
        String codServ = tbConsultaServico.getCOD_SERVICO().asString();
        if (!codServ.equals("")) {
            tbServicosAdicionais.close();
            tbServicosAdicionais.clearFilters();
            tbServicosAdicionais.clearParams();
            tbServicosAdicionais.addFilter("COD_SERVICO");
            tbServicosAdicionais.addParam("COD_SERVICO", codServ);
            tbServicosAdicionais.setOrderBy("DESCRICAO_ADICIONAL");
            tbServicosAdicionais.open();

            if (TableState.MODIFYING.equals(tbServicos.getState()) && !tbServicos.getCOD_SERVICO().asString().equals(codServ)) {
                carregaServicos(codServ);
            }
        } else {
            tbServicosAdicionais.close();
            tbServicosAdicionais.clearFilters();
            tbServicosAdicionais.clearParams();
        }

    }

    public void carregaModelos() throws DataException {
        String codServ = tbConsultaServico.getCOD_SERVICO().asString();

        if (!codServ.equals("")) {
            tbTemposPadroes.close();
            tbTemposPadroes.clearFilters();
            tbTemposPadroes.clearParams();
            tbTemposPadroes.addFilter("COD_SERVICO_UPPER");
            tbTemposPadroes.addParam("COD_SERVICO_UPPER", codServ);
            tbTemposPadroes.setOrderBy("MODELO");
            tbTemposPadroes.open();

            if (TableState.MODIFYING.equals(tbServicos.getState()) && !tbServicos.getCOD_SERVICO().asString().equals(codServ)) {
                carregaServicos(codServ);
            }
        } else {
            tbTemposPadroes.close();
            tbTemposPadroes.clearFilters();
            tbTemposPadroes.clearParams();
        }


    }

    public void carregaServicos(String codServico) throws DataException {
        tbServicos.close();
        tbServicos.clearFilters();
        tbServicos.clearParams();
        tbServicos.addFilter("COD_SERVICO");
        tbServicos.addParam("COD_SERVICO", codServico);
        tbServicos.open();
    }
}
