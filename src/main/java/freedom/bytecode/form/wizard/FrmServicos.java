package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmServicos extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ServicosRNA rn = null;

    public FrmServicos() {
        try {
            rn = (freedom.bytecode.rn.ServicosRNA) getRN(freedom.bytecode.rn.wizard.ServicosRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbServicos();
        init_tbConsultaServico();
        init_tbServicosGrupo();
        init_tbServicosSubGrupo();
        init_tbServicosSetores();
        init_tbClassificacaoServ();
        init_tbServicosGrupoCadastro();
        init_tbServicosSubGrupoCadastro();
        init_tbClassificacaoServCadastro();
        init_tbServicosSetoresCadastro();
        init_tbReinfClassifEfd();
        init_tbEmpresas();
        init_tbServicosPrefeitura();
        init_tbServicosRedBcIss();
        init_tbServicosAdicionais();
        init_tbMarcas();
        init_tbEpecaProdutos();
        init_tbProdutos();
        init_tbProdutosModelos();
        init_tbConcessionariaTipo();
        init_tbTemposPadroes();
        init_tbServicoMarca();
        init_tbServicosLc1162003();
        init_tbReclamacaoServicosEmpresa();
        init_tbComboClassificacaoServ();
        init_tbTemposPadroesValida();
        init_pmGridServicos();
        init_miExportarExcelServicos();
        init_pmGridEmpresasImposto();
        init_miGridEmpresasImposto();
        init_pmGridServicosPrefeitura();
        init_miGridServicosPrefeitura();
        init_pmGridServicosRedBcIss();
        init_miGridServicosRedBcIss();
        init_pmGridAdicionais();
        init_miGridAdicionais();
        init_pmGridModelos();
        init_miGridModelos();
        init_FVBox1();
        init_hBoxBotoes();
        init_btnPesquisaServico();
        init_btnNovo();
        init_btnAlterar();
        init_btnExcluir();
        init_btnSalvar();
        init_btnCancelar();
        init_btnIndicadores();
        init_hboxCorChip();
        init_vboxCorChip();
        init_FLabel41();
        init_cbCorChip();
        init_pageControlServico();
        init_tabSheetListagem();
        init_FBorderPanel1();
        init_FVBoxGridPrincipal();
        init_gridServicos();
        init_FVBoxGrFiltrosGeral();
        init_FHBoxGrFiltros1();
        init_cbGrupoFiltro();
        init_cbSbGrupoFiltro();
        init_FHBoxGrFiltros2();
        init_cbSetorFiltro();
        init_cbClassifFiltro();
        init_FHBoxGrFiltros3();
        init_cbAtivosFiltro();
        init_cbOriginaisFiltro();
        init_FHBoxGrFiltros4();
        init_cbCobrarFiltro();
        init_FHBox2();
        init_FVBox14();
        init_FHBox39();
        init_FLabel8();
        init_FVBox75();
        init_FStrCodServFiltro();
        init_FVBox17();
        init_FHBox33();
        init_FVBox2();
        init_FLabel9();
        init_chkLikeDescricao();
        init_FStrDescFiltro();
        init_FVBox70();
        init_FHBox38();
        init_lblOSFiltro();
        init_FVBox74();
        init_nbNumeroOS();
        init_FVBox3();
        init_lblFlagsFiltros();
        init_v();
        init_ckbTerceiros();
        init_ckbTrocaOleo();
        init_ckbLubrificacao();
        init_ckbLavagem();
        init_ckbServicoTZero();
        init_lblFlagCruzamentos();
        init_FHBox4();
        init_ckbMarca();
        init_ckbAdicionais();
        init_ckbTMO();
        init_FHBox6();
        init_FVBox37();
        init_lblFlagApontamento();
        init_FHBox5();
        init_ckbNPermitir();
        init_ckbForcar();
        init_FVBox56();
        init_lblNfRemessa();
        init_FHBox10();
        init_ckbRemessa();
        init_ckbPassRemessa();
        init_FHBox8();
        init_FVBox4();
        init_FLabel1();
        init_FHBox19();
        init_FVBox29();
        init_ckbRetemIRRF();
        init_ckbRetemINSS();
        init_FVBox62();
        init_ckbNRetemPccFiltro();
        init_ckbNaoReter();
        init_FVBox18();
        init_FLabel38();
        init_FVBox81();
        init_ckbBmwIspa();
        init_ckbAutFabrica();
        init_BtnLimparFiltros();
        init_tabCadastro();
        init_vBoxTabCadastro();
        init_FHBox1();
        init_FVBox6();
        init_FLabel2();
        init_FIntCod();
        init_FVBox7();
        init_FLabel3();
        init_FStrFabricante();
        init_FVBox79();
        init_FHBox41();
        init_chkOriginal();
        init_FLabel6();
        init_FStrDesc();
        init_FLabel7();
        init_FMemDetalhe();
        init_FStrAviso();
        init_FHBox11();
        init_FVBox57();
        init_FLabel17();
        init_FCoGrupoCad();
        init_FVBox58();
        init_FLabel27();
        init_FCoClassCad();
        init_FHBox12();
        init_FVBox59();
        init_FLabel35();
        init_FCoSubGrupoCad();
        init_FVBox60();
        init_FLabel37();
        init_FCoSetorCad();
        init_FHBox13();
        init_FVBox8();
        init_FLabel4();
        init_FCbCodigoLC();
        init_FVBox10();
        init_FLabel5();
        init_FHBox26();
        init_FStrCodNbsDesc();
        init_btnPesqCodNBS();
        init_FStrCodNBS();
        init_FHBox14();
        init_FVBox11();
        init_FHBox16();
        init_FVBox16();
        init_ckbTerCad();
        init_strPesqCad();
        init_FBtnPesquisar();
        init_FIntCodCliente();
        init_FVBox12();
        init_FHBox20();
        init_FVBox13();
        init_ckbLubCad();
        init_ckbTrocaOleoCad();
        init_ckbLavagemCad();
        init_FVBox15();
        init_ckbPassiRemeCad();
        init_ckbNApontarCad();
        init_ckbTerRemeCad();
        init_FVBox64();
        init_ckbNForcarCad();
        init_ckbAtivo();
        init_tabValor();
        init_FVBox19();
        init_FVBox20();
        init_lblDescServicVal();
        init_FVBox61();
        init_FLabel39();
        init_FCombo3();
        init_lblTempo();
        init_FHBox9();
        init_FVBox21();
        init_FLabel10();
        init_FDecTmoP();
        init_FVBox22();
        init_FLabel11();
        init_FDecAgend();
        init_FVBox23();
        init_FLabel12();
        init_FDecDuoTec();
        init_FLabel13();
        init_FHBox15();
        init_FVBox24();
        init_FLabel14();
        init_FDecPrecVen();
        init_FVBox25();
        init_FLabel15();
        init_FDecPrecCus();
        init_FLabel16();
        init_FHBox17();
        init_FCombo2();
        init_FDecimal1();
        init_FTabImposto();
        init_vBoxImposto();
        init_FVBox27();
        init_lblDescServicImp();
        init_FHBox35();
        init_FVBox66();
        init_FHBox3();
        init_FVBox68();
        init_ckbRetemIrrfCad();
        init_FDecAliqIRRF();
        init_FVBox30();
        init_FLabel18();
        init_FHBox18();
        init_FVBox69();
        init_ckbRetemInssCad();
        init_FDecAliqInss();
        init_FVBox31();
        init_FLabel19();
        init_FVBox67();
        init_FVBox65();
        init_cknNRetemPCC();
        init_FHBox7();
        init_FVBox28();
        init_FLabel40();
        init_cbClassReinfCad();
        init_FHBox21();
        init_hBoxGridEmpresaTabImposto();
        init_gridEmpresasImposto();
        init_FHBox40();
        init_FVBox78();
        init_FVBox33();
        init_FVBox71();
        init_FVBox5();
        init_btnInsCodPref();
        init_btnExcCodPref();
        init_FVBox9();
        init_FVBox34();
        init_FVBox36();
        init_FLabel20();
        init_FStrCodPref();
        init_FVBox73();
        init_FVBox38();
        init_FVBox26();
        init_btnInsRedBase();
        init_btnExcRedBase();
        init_FVBox77();
        init_FVBox39();
        init_FLabel21();
        init_FHBox25();
        init_FDecAliqRed();
        init_FLabel22();
        init_FVBox35();
        init_FHBox36();
        init_FVBox32();
        init_FLabel33();
        init_gridServicosPrefeitura();
        init_FHBox37();
        init_FVBox72();
        init_FLabel34();
        init_gridServicosRedBcIss();
        init_FTabAdicionais();
        init_FVBox40();
        init_FVBox41();
        init_lblDescServAd();
        init_FGridAdicionais();
        init_FHBox22();
        init_btnNovoAd();
        init_btnAlterarrAd();
        init_btnSalvarAd();
        init_btnCancelAd();
        init_FVBox42();
        init_FLabel23();
        init_FString2();
        init_FHBox23();
        init_FVBox43();
        init_FLabel24();
        init_FDecTmoAdic();
        init_FVBox44();
        init_FLabel25();
        init_FDecPrecVenAdic();
        init_FVBox45();
        init_FLabel26();
        init_FDecPrecoCustoAdic();
        init_FTabsheetModelos();
        init_FVBox46();
        init_FVBox47();
        init_lblDescServicMod();
        init_FVBox63();
        init_FHBox24();
        init_FLabel28();
        init_FHBox27();
        init_FHBox28();
        init_cbMarcaCad();
        init_cbFamiliaCad();
        init_cbModeloCad();
        init_FGridModelos();
        init_FHBox29();
        init_btnNovoModelos();
        init_btnAlterarModelo();
        init_btnSalvarModelos();
        init_btnCancelarModelos();
        init_FHBox30();
        init_FVBox48();
        init_FLabel29();
        init_FStrModeloPesq();
        init_FVBox49();
        init_btnPesqModelo();
        init_FIntCodMod();
        init_FIntCodProduto();
        init_FHBox31();
        init_FVBox50();
        init_FLabel30();
        init_FDecTMOPadraoMod();
        init_FVBox51();
        init_FLabel31();
        init_FDecAgendaModelos();
        init_FVBox52();
        init_FLabel32();
        init_FDecDuoModelos();
        init_FVBox53();
        init_ckbAtivoModelo();
        init_FTabMarca();
        init_FVBox54();
        init_FVBox55();
        init_lblDescServicMarcas();
        init_FHBox32();
        init_FLabel36();
        init_FHBox34();
        init_FDualListMarca();
        init_scServicos();
        init_FrmServicos();
    }

    public SERVICOS tbServicos;

    private void init_tbServicos() {
        tbServicos = rn.tbServicos;
        tbServicos.setName("tbServicos");
        tbServicos.setMaxRowCount(0);
        tbServicos.setWKey("4600191;46001");
        tbServicos.setRatioBatchSize(20);
        getTables().put(tbServicos, "tbServicos");
        tbServicos.applyProperties();
    }

    public CONSULTA_SERVICO tbConsultaServico;

    private void init_tbConsultaServico() {
        tbConsultaServico = rn.tbConsultaServico;
        tbConsultaServico.setName("tbConsultaServico");
        tbConsultaServico.setMaxRowCount(0);
        tbConsultaServico.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbConsultaServicoAfterScroll(event);
            processarFlow("FrmServicos", "tbConsultaServico", "OnAfterScroll");
        });
        tbConsultaServico.setWKey("4600191;46002");
        tbConsultaServico.setRatioBatchSize(20);
        getTables().put(tbConsultaServico, "tbConsultaServico");
        tbConsultaServico.applyProperties();
    }

    public SERVICOS_GRUPO tbServicosGrupo;

    private void init_tbServicosGrupo() {
        tbServicosGrupo = rn.tbServicosGrupo;
        tbServicosGrupo.setName("tbServicosGrupo");
        tbServicosGrupo.setMaxRowCount(0);
        tbServicosGrupo.setWKey("4600191;46004");
        tbServicosGrupo.setRatioBatchSize(20);
        getTables().put(tbServicosGrupo, "tbServicosGrupo");
        tbServicosGrupo.applyProperties();
    }

    public SERVICOS_SUB_GRUPO tbServicosSubGrupo;

    private void init_tbServicosSubGrupo() {
        tbServicosSubGrupo = rn.tbServicosSubGrupo;
        tbServicosSubGrupo.setName("tbServicosSubGrupo");
        tbServicosSubGrupo.setMasterFields("COD_GRUPO_SERV");
        tbServicosSubGrupo.setDetailFilters("COD_GRUPO_SERV");
        tbServicosSubGrupo.setMaxRowCount(0);
        tbServicosSubGrupo.setMasterTable(tbServicosGrupo);
        tbServicosSubGrupo.setWKey("4600191;46005");
        tbServicosSubGrupo.setRatioBatchSize(20);
        getTables().put(tbServicosSubGrupo, "tbServicosSubGrupo");
        tbServicosSubGrupo.applyProperties();
    }

    public SERVICOS_SETORES tbServicosSetores;

    private void init_tbServicosSetores() {
        tbServicosSetores = rn.tbServicosSetores;
        tbServicosSetores.setName("tbServicosSetores");
        tbServicosSetores.setMaxRowCount(0);
        tbServicosSetores.setWKey("4600191;46006");
        tbServicosSetores.setRatioBatchSize(20);
        getTables().put(tbServicosSetores, "tbServicosSetores");
        tbServicosSetores.applyProperties();
    }

    public LR_CLASSIFICACAO_SERV tbClassificacaoServ;

    private void init_tbClassificacaoServ() {
        tbClassificacaoServ = rn.tbClassificacaoServ;
        tbClassificacaoServ.setName("tbClassificacaoServ");
        tbClassificacaoServ.setMaxRowCount(0);
        tbClassificacaoServ.setWKey("4600191;46007");
        tbClassificacaoServ.setRatioBatchSize(20);
        getTables().put(tbClassificacaoServ, "tbClassificacaoServ");
        tbClassificacaoServ.applyProperties();
    }

    public SERVICOS_GRUPO tbServicosGrupoCadastro;

    private void init_tbServicosGrupoCadastro() {
        tbServicosGrupoCadastro = rn.tbServicosGrupoCadastro;
        tbServicosGrupoCadastro.setName("tbServicosGrupoCadastro");
        tbServicosGrupoCadastro.setMaxRowCount(0);
        tbServicosGrupoCadastro.setWKey("4600191;46008");
        tbServicosGrupoCadastro.setRatioBatchSize(20);
        getTables().put(tbServicosGrupoCadastro, "tbServicosGrupoCadastro");
        tbServicosGrupoCadastro.applyProperties();
    }

    public SERVICOS_SUB_GRUPO tbServicosSubGrupoCadastro;

    private void init_tbServicosSubGrupoCadastro() {
        tbServicosSubGrupoCadastro = rn.tbServicosSubGrupoCadastro;
        tbServicosSubGrupoCadastro.setName("tbServicosSubGrupoCadastro");
        tbServicosSubGrupoCadastro.setMasterFields("COD_GRUPO_SERV");
        tbServicosSubGrupoCadastro.setDetailFilters("COD_GRUPO_SERV");
        tbServicosSubGrupoCadastro.setMaxRowCount(0);
        tbServicosSubGrupoCadastro.setMasterTable(tbServicosGrupoCadastro);
        tbServicosSubGrupoCadastro.setWKey("4600191;46009");
        tbServicosSubGrupoCadastro.setRatioBatchSize(20);
        getTables().put(tbServicosSubGrupoCadastro, "tbServicosSubGrupoCadastro");
        tbServicosSubGrupoCadastro.applyProperties();
    }

    public LR_CLASSIFICACAO_SERV tbClassificacaoServCadastro;

    private void init_tbClassificacaoServCadastro() {
        tbClassificacaoServCadastro = rn.tbClassificacaoServCadastro;
        tbClassificacaoServCadastro.setName("tbClassificacaoServCadastro");
        tbClassificacaoServCadastro.setMaxRowCount(0);
        tbClassificacaoServCadastro.setWKey("4600191;460010");
        tbClassificacaoServCadastro.setRatioBatchSize(20);
        getTables().put(tbClassificacaoServCadastro, "tbClassificacaoServCadastro");
        tbClassificacaoServCadastro.applyProperties();
    }

    public SERVICOS_SETORES tbServicosSetoresCadastro;

    private void init_tbServicosSetoresCadastro() {
        tbServicosSetoresCadastro = rn.tbServicosSetoresCadastro;
        tbServicosSetoresCadastro.setName("tbServicosSetoresCadastro");
        tbServicosSetoresCadastro.setMaxRowCount(0);
        tbServicosSetoresCadastro.setWKey("4600191;460011");
        tbServicosSetoresCadastro.setRatioBatchSize(20);
        getTables().put(tbServicosSetoresCadastro, "tbServicosSetoresCadastro");
        tbServicosSetoresCadastro.applyProperties();
    }

    public REINF_CLASSIF_EFD tbReinfClassifEfd;

    private void init_tbReinfClassifEfd() {
        tbReinfClassifEfd = rn.tbReinfClassifEfd;
        tbReinfClassifEfd.setName("tbReinfClassifEfd");
        tbReinfClassifEfd.setMaxRowCount(0);
        tbReinfClassifEfd.setWKey("4600191;460012");
        tbReinfClassifEfd.setRatioBatchSize(20);
        getTables().put(tbReinfClassifEfd, "tbReinfClassifEfd");
        tbReinfClassifEfd.applyProperties();
    }

    public EMPRESAS tbEmpresas;

    private void init_tbEmpresas() {
        tbEmpresas = rn.tbEmpresas;
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(0);
        tbEmpresas.setWKey("4600191;460013");
        tbEmpresas.setRatioBatchSize(20);
        getTables().put(tbEmpresas, "tbEmpresas");
        tbEmpresas.applyProperties();
    }

    public SERVICOS_PREFEITURA tbServicosPrefeitura;

    private void init_tbServicosPrefeitura() {
        tbServicosPrefeitura = rn.tbServicosPrefeitura;
        tbServicosPrefeitura.setName("tbServicosPrefeitura");
        tbServicosPrefeitura.setMaxRowCount(0);
        tbServicosPrefeitura.setWKey("4600191;460015");
        tbServicosPrefeitura.setRatioBatchSize(20);
        getTables().put(tbServicosPrefeitura, "tbServicosPrefeitura");
        tbServicosPrefeitura.applyProperties();
    }

    public SERVICOS_RED_BC_ISS tbServicosRedBcIss;

    private void init_tbServicosRedBcIss() {
        tbServicosRedBcIss = rn.tbServicosRedBcIss;
        tbServicosRedBcIss.setName("tbServicosRedBcIss");
        tbServicosRedBcIss.setMaxRowCount(0);
        tbServicosRedBcIss.setWKey("4600191;460019");
        tbServicosRedBcIss.setRatioBatchSize(20);
        getTables().put(tbServicosRedBcIss, "tbServicosRedBcIss");
        tbServicosRedBcIss.applyProperties();
    }

    public SERVICOS_ADICIONAIS tbServicosAdicionais;

    private void init_tbServicosAdicionais() {
        tbServicosAdicionais = rn.tbServicosAdicionais;
        tbServicosAdicionais.setName("tbServicosAdicionais");
        tbServicosAdicionais.setMaxRowCount(0);
        tbServicosAdicionais.setWKey("4600191;460020");
        tbServicosAdicionais.setRatioBatchSize(20);
        getTables().put(tbServicosAdicionais, "tbServicosAdicionais");
        tbServicosAdicionais.applyProperties();
    }

    public MARCAS tbMarcas;

    private void init_tbMarcas() {
        tbMarcas = rn.tbMarcas;
        tbMarcas.setName("tbMarcas");
        tbMarcas.setMaxRowCount(0);
        tbMarcas.setWKey("4600191;460021");
        tbMarcas.setRatioBatchSize(20);
        getTables().put(tbMarcas, "tbMarcas");
        tbMarcas.applyProperties();
    }

    public HAR_EPECA_PRODUTOS tbEpecaProdutos;

    private void init_tbEpecaProdutos() {
        tbEpecaProdutos = rn.tbEpecaProdutos;
        tbEpecaProdutos.setName("tbEpecaProdutos");
        tbEpecaProdutos.setMaxRowCount(0);
        tbEpecaProdutos.setWKey("4600191;460022");
        tbEpecaProdutos.setRatioBatchSize(20);
        getTables().put(tbEpecaProdutos, "tbEpecaProdutos");
        tbEpecaProdutos.applyProperties();
    }

    public PRODUTOS tbProdutos;

    private void init_tbProdutos() {
        tbProdutos = rn.tbProdutos;
        tbProdutos.setName("tbProdutos");
        tbProdutos.setMaxRowCount(0);
        tbProdutos.setWKey("4600191;460023");
        tbProdutos.setRatioBatchSize(20);
        getTables().put(tbProdutos, "tbProdutos");
        tbProdutos.applyProperties();
    }

    public PRODUTOS_MODELOS tbProdutosModelos;

    private void init_tbProdutosModelos() {
        tbProdutosModelos = rn.tbProdutosModelos;
        tbProdutosModelos.setName("tbProdutosModelos");
        tbProdutosModelos.setMaxRowCount(0);
        tbProdutosModelos.setWKey("4600191;460024");
        tbProdutosModelos.setRatioBatchSize(20);
        getTables().put(tbProdutosModelos, "tbProdutosModelos");
        tbProdutosModelos.applyProperties();
    }

    public CONCESSIONARIA_TIPO tbConcessionariaTipo;

    private void init_tbConcessionariaTipo() {
        tbConcessionariaTipo = rn.tbConcessionariaTipo;
        tbConcessionariaTipo.setName("tbConcessionariaTipo");
        tbConcessionariaTipo.setMaxRowCount(0);
        tbConcessionariaTipo.addEventListener("onBeforePost", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbConcessionariaTipoBeforePost(event);
            processarFlow("FrmServicos", "tbConcessionariaTipo", "OnBeforePost");
        });
        tbConcessionariaTipo.setWKey("4600191;460026");
        tbConcessionariaTipo.setRatioBatchSize(20);
        getTables().put(tbConcessionariaTipo, "tbConcessionariaTipo");
        tbConcessionariaTipo.applyProperties();
    }

    public TEMPOS_PADROES tbTemposPadroes;

    private void init_tbTemposPadroes() {
        tbTemposPadroes = rn.tbTemposPadroes;
        tbTemposPadroes.setName("tbTemposPadroes");
        tbTemposPadroes.setMaxRowCount(0);
        tbTemposPadroes.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbTemposPadroesAfterScroll(event);
            processarFlow("FrmServicos", "tbTemposPadroes", "OnAfterScroll");
        });
        tbTemposPadroes.setWKey("4600191;460029");
        tbTemposPadroes.setRatioBatchSize(20);
        getTables().put(tbTemposPadroes, "tbTemposPadroes");
        tbTemposPadroes.applyProperties();
    }

    public SERVICO_MARCA tbServicoMarca;

    private void init_tbServicoMarca() {
        tbServicoMarca = rn.tbServicoMarca;
        tbServicoMarca.setName("tbServicoMarca");
        tbServicoMarca.setMaxRowCount(0);
        tbServicoMarca.addEventListener("onBeforePost", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbServicoMarcaBeforePost(event);
            processarFlow("FrmServicos", "tbServicoMarca", "OnBeforePost");
        });
        tbServicoMarca.setWKey("4600191;460031");
        tbServicoMarca.setRatioBatchSize(20);
        getTables().put(tbServicoMarca, "tbServicoMarca");
        tbServicoMarca.applyProperties();
    }

    public SERVICOS_LC116_2003 tbServicosLc1162003;

    private void init_tbServicosLc1162003() {
        tbServicosLc1162003 = rn.tbServicosLc1162003;
        tbServicosLc1162003.setName("tbServicosLc1162003");
        tbServicosLc1162003.setMaxRowCount(0);
        tbServicosLc1162003.setWKey("4600191;460032");
        tbServicosLc1162003.setRatioBatchSize(20);
        getTables().put(tbServicosLc1162003, "tbServicosLc1162003");
        tbServicosLc1162003.applyProperties();
    }

    public RECLAMACAO_SERVICOS_EMPRESA tbReclamacaoServicosEmpresa;

    private void init_tbReclamacaoServicosEmpresa() {
        tbReclamacaoServicosEmpresa = rn.tbReclamacaoServicosEmpresa;
        tbReclamacaoServicosEmpresa.setName("tbReclamacaoServicosEmpresa");
        tbReclamacaoServicosEmpresa.setMaxRowCount(200);
        tbReclamacaoServicosEmpresa.setWKey("4600191;460033");
        tbReclamacaoServicosEmpresa.setRatioBatchSize(20);
        getTables().put(tbReclamacaoServicosEmpresa, "tbReclamacaoServicosEmpresa");
        tbReclamacaoServicosEmpresa.applyProperties();
    }

    public CLASSIFICACAO_SERV tbComboClassificacaoServ;

    private void init_tbComboClassificacaoServ() {
        tbComboClassificacaoServ = rn.tbComboClassificacaoServ;
        tbComboClassificacaoServ.setName("tbComboClassificacaoServ");
        tbComboClassificacaoServ.setMaxRowCount(200);
        tbComboClassificacaoServ.setWKey("4600191;460034");
        tbComboClassificacaoServ.setRatioBatchSize(20);
        getTables().put(tbComboClassificacaoServ, "tbComboClassificacaoServ");
        tbComboClassificacaoServ.applyProperties();
    }

    public TEMPOS_PADROES tbTemposPadroesValida;

    private void init_tbTemposPadroesValida() {
        tbTemposPadroesValida = rn.tbTemposPadroesValida;
        tbTemposPadroesValida.setName("tbTemposPadroesValida");
        tbTemposPadroesValida.setMaxRowCount(200);
        tbTemposPadroesValida.setWKey("4600191;42101");
        tbTemposPadroesValida.setRatioBatchSize(20);
        getTables().put(tbTemposPadroesValida, "tbTemposPadroesValida");
        tbTemposPadroesValida.applyProperties();
    }

    public TFPopupMenu pmGridServicos = new TFPopupMenu();

    private void init_pmGridServicos() {
        pmGridServicos.setName("pmGridServicos");
        FrmServicos.addChildren(pmGridServicos);
        pmGridServicos.applyProperties();
    }

    public TFMenuItem miExportarExcelServicos = new TFMenuItem();

    private void init_miExportarExcelServicos() {
        miExportarExcelServicos.setName("miExportarExcelServicos");
        miExportarExcelServicos.setCaption("Exportar para Excel");
        miExportarExcelServicos.setImageIndex(22004);
        miExportarExcelServicos.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            miExportarExcelServicosClick(event);
            processarFlow("FrmServicos", "miExportarExcelServicos", "OnClick");
        });
        miExportarExcelServicos.setAccess(false);
        miExportarExcelServicos.setCheckmark(false);
        pmGridServicos.addChildren(miExportarExcelServicos);
        miExportarExcelServicos.applyProperties();
    }

    public TFPopupMenu pmGridEmpresasImposto = new TFPopupMenu();

    private void init_pmGridEmpresasImposto() {
        pmGridEmpresasImposto.setName("pmGridEmpresasImposto");
        FrmServicos.addChildren(pmGridEmpresasImposto);
        pmGridEmpresasImposto.applyProperties();
    }

    public TFMenuItem miGridEmpresasImposto = new TFMenuItem();

    private void init_miGridEmpresasImposto() {
        miGridEmpresasImposto.setName("miGridEmpresasImposto");
        miGridEmpresasImposto.setCaption("Exportar para Excel");
        miGridEmpresasImposto.setImageIndex(22004);
        miGridEmpresasImposto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            miGridEmpresasImpostoClick(event);
            processarFlow("FrmServicos", "miGridEmpresasImposto", "OnClick");
        });
        miGridEmpresasImposto.setAccess(false);
        miGridEmpresasImposto.setCheckmark(false);
        pmGridEmpresasImposto.addChildren(miGridEmpresasImposto);
        miGridEmpresasImposto.applyProperties();
    }

    public TFPopupMenu pmGridServicosPrefeitura = new TFPopupMenu();

    private void init_pmGridServicosPrefeitura() {
        pmGridServicosPrefeitura.setName("pmGridServicosPrefeitura");
        FrmServicos.addChildren(pmGridServicosPrefeitura);
        pmGridServicosPrefeitura.applyProperties();
    }

    public TFMenuItem miGridServicosPrefeitura = new TFMenuItem();

    private void init_miGridServicosPrefeitura() {
        miGridServicosPrefeitura.setName("miGridServicosPrefeitura");
        miGridServicosPrefeitura.setCaption("Exportar para Excel");
        miGridServicosPrefeitura.setImageIndex(22004);
        miGridServicosPrefeitura.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            miGridServicosPrefeituraClick(event);
            processarFlow("FrmServicos", "miGridServicosPrefeitura", "OnClick");
        });
        miGridServicosPrefeitura.setAccess(false);
        miGridServicosPrefeitura.setCheckmark(false);
        pmGridServicosPrefeitura.addChildren(miGridServicosPrefeitura);
        miGridServicosPrefeitura.applyProperties();
    }

    public TFPopupMenu pmGridServicosRedBcIss = new TFPopupMenu();

    private void init_pmGridServicosRedBcIss() {
        pmGridServicosRedBcIss.setName("pmGridServicosRedBcIss");
        FrmServicos.addChildren(pmGridServicosRedBcIss);
        pmGridServicosRedBcIss.applyProperties();
    }

    public TFMenuItem miGridServicosRedBcIss = new TFMenuItem();

    private void init_miGridServicosRedBcIss() {
        miGridServicosRedBcIss.setName("miGridServicosRedBcIss");
        miGridServicosRedBcIss.setCaption("Exportar para Excel");
        miGridServicosRedBcIss.setImageIndex(22004);
        miGridServicosRedBcIss.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            miGridServicosRedBcIssClick(event);
            processarFlow("FrmServicos", "miGridServicosRedBcIss", "OnClick");
        });
        miGridServicosRedBcIss.setAccess(false);
        miGridServicosRedBcIss.setCheckmark(false);
        pmGridServicosRedBcIss.addChildren(miGridServicosRedBcIss);
        miGridServicosRedBcIss.applyProperties();
    }

    public TFPopupMenu pmGridAdicionais = new TFPopupMenu();

    private void init_pmGridAdicionais() {
        pmGridAdicionais.setName("pmGridAdicionais");
        FrmServicos.addChildren(pmGridAdicionais);
        pmGridAdicionais.applyProperties();
    }

    public TFMenuItem miGridAdicionais = new TFMenuItem();

    private void init_miGridAdicionais() {
        miGridAdicionais.setName("miGridAdicionais");
        miGridAdicionais.setCaption("Exportar para Excel");
        miGridAdicionais.setImageIndex(22004);
        miGridAdicionais.setAccess(false);
        miGridAdicionais.setCheckmark(false);
        pmGridAdicionais.addChildren(miGridAdicionais);
        miGridAdicionais.applyProperties();
    }

    public TFPopupMenu pmGridModelos = new TFPopupMenu();

    private void init_pmGridModelos() {
        pmGridModelos.setName("pmGridModelos");
        FrmServicos.addChildren(pmGridModelos);
        pmGridModelos.applyProperties();
    }

    public TFMenuItem miGridModelos = new TFMenuItem();

    private void init_miGridModelos() {
        miGridModelos.setName("miGridModelos");
        miGridModelos.setCaption("Exportar para Excel");
        miGridModelos.setImageIndex(22004);
        miGridModelos.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            miGridModelosClick(event);
            processarFlow("FrmServicos", "miGridModelos", "OnClick");
        });
        miGridModelos.setAccess(false);
        miGridModelos.setCheckmark(false);
        pmGridModelos.addChildren(miGridModelos);
        miGridModelos.applyProperties();
    }

    protected TFForm FrmServicos = this;
    private void init_FrmServicos() {
        FrmServicos.setName("FrmServicos");
        FrmServicos.setCaption("Servi\u00E7os");
        FrmServicos.setClientHeight(750);
        FrmServicos.setClientWidth(562);
        FrmServicos.setColor("clBtnFace");
        FrmServicos.setWOrigem("EhMain");
        FrmServicos.setWKey("4600191");
        FrmServicos.setSpacing(0);
        FrmServicos.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(562);
        FVBox1.setHeight(750);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(5);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmServicos.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(0);
        hBoxBotoes.setWidth(576);
        hBoxBotoes.setHeight(65);
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(0);
        hBoxBotoes.setPaddingLeft(0);
        hBoxBotoes.setPaddingRight(0);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(5);
        hBoxBotoes.setFlexVflex("ftFalse");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        FVBox1.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFButton btnPesquisaServico = new TFButton();

    private void init_btnPesquisaServico() {
        btnPesquisaServico.setName("btnPesquisaServico");
        btnPesquisaServico.setLeft(0);
        btnPesquisaServico.setTop(0);
        btnPesquisaServico.setWidth(65);
        btnPesquisaServico.setHeight(56);
        btnPesquisaServico.setCaption("Pesquisar");
        btnPesquisaServico.setFontColor("clWindowText");
        btnPesquisaServico.setFontSize(-11);
        btnPesquisaServico.setFontName("Tahoma");
        btnPesquisaServico.setFontStyle("[]");
        btnPesquisaServico.setLayout("blGlyphTop");
        btnPesquisaServico.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisaServicoClick(event);
            processarFlow("FrmServicos", "btnPesquisaServico", "OnClick");
        });
        btnPesquisaServico.setImageId(13);
        btnPesquisaServico.setColor("clBtnFace");
        btnPesquisaServico.setAccess(false);
        btnPesquisaServico.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnPesquisaServico);
        btnPesquisaServico.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(65);
        btnNovo.setTop(0);
        btnNovo.setWidth(65);
        btnNovo.setHeight(56);
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmServicos", "btnNovo", "OnClick");
        });
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(false);
        btnNovo.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(130);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(65);
        btnAlterar.setHeight(56);
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmServicos", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(false);
        btnAlterar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(195);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(65);
        btnExcluir.setHeight(56);
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.setVisible(false);
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmServicos", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(8);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(false);
        btnExcluir.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(260);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(56);
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmServicos", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(325);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(56);
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmServicos", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFButton btnIndicadores = new TFButton();

    private void init_btnIndicadores() {
        btnIndicadores.setName("btnIndicadores");
        btnIndicadores.setLeft(390);
        btnIndicadores.setTop(0);
        btnIndicadores.setWidth(65);
        btnIndicadores.setHeight(56);
        btnIndicadores.setCaption("Indicadores");
        btnIndicadores.setFontColor("clWindowText");
        btnIndicadores.setFontSize(-11);
        btnIndicadores.setFontName("Tahoma");
        btnIndicadores.setFontStyle("[]");
        btnIndicadores.setLayout("blGlyphTop");
        btnIndicadores.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnIndicadoresClick(event);
            processarFlow("FrmServicos", "btnIndicadores", "OnClick");
        });
        btnIndicadores.setImageId(4600150);
        btnIndicadores.setColor("clBtnFace");
        btnIndicadores.setAccess(false);
        btnIndicadores.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnIndicadores);
        btnIndicadores.applyProperties();
    }

    public TFHBox hboxCorChip = new TFHBox();

    private void init_hboxCorChip() {
        hboxCorChip.setName("hboxCorChip");
        hboxCorChip.setLeft(455);
        hboxCorChip.setTop(0);
        hboxCorChip.setWidth(185);
        hboxCorChip.setHeight(60);
        hboxCorChip.setBorderStyle("stNone");
        hboxCorChip.setPaddingTop(0);
        hboxCorChip.setPaddingLeft(0);
        hboxCorChip.setPaddingRight(0);
        hboxCorChip.setPaddingBottom(0);
        hboxCorChip.setMarginTop(0);
        hboxCorChip.setMarginLeft(0);
        hboxCorChip.setMarginRight(0);
        hboxCorChip.setMarginBottom(0);
        hboxCorChip.setSpacing(1);
        hboxCorChip.setFlexVflex("ftFalse");
        hboxCorChip.setFlexHflex("ftTrue");
        hboxCorChip.setScrollable(false);
        hboxCorChip.setBoxShadowConfigHorizontalLength(10);
        hboxCorChip.setBoxShadowConfigVerticalLength(10);
        hboxCorChip.setBoxShadowConfigBlurRadius(5);
        hboxCorChip.setBoxShadowConfigSpreadRadius(0);
        hboxCorChip.setBoxShadowConfigShadowColor("clBlack");
        hboxCorChip.setBoxShadowConfigOpacity(75);
        hboxCorChip.setVAlign("tvTop");
        hBoxBotoes.addChildren(hboxCorChip);
        hboxCorChip.applyProperties();
    }

    public TFVBox vboxCorChip = new TFVBox();

    private void init_vboxCorChip() {
        vboxCorChip.setName("vboxCorChip");
        vboxCorChip.setLeft(0);
        vboxCorChip.setTop(0);
        vboxCorChip.setWidth(125);
        vboxCorChip.setHeight(59);
        vboxCorChip.setBorderStyle("stNone");
        vboxCorChip.setPaddingTop(0);
        vboxCorChip.setPaddingLeft(0);
        vboxCorChip.setPaddingRight(0);
        vboxCorChip.setPaddingBottom(0);
        vboxCorChip.setMarginTop(0);
        vboxCorChip.setMarginLeft(0);
        vboxCorChip.setMarginRight(0);
        vboxCorChip.setMarginBottom(0);
        vboxCorChip.setSpacing(1);
        vboxCorChip.setFlexVflex("ftFalse");
        vboxCorChip.setFlexHflex("ftFalse");
        vboxCorChip.setScrollable(false);
        vboxCorChip.setBoxShadowConfigHorizontalLength(10);
        vboxCorChip.setBoxShadowConfigVerticalLength(10);
        vboxCorChip.setBoxShadowConfigBlurRadius(5);
        vboxCorChip.setBoxShadowConfigSpreadRadius(0);
        vboxCorChip.setBoxShadowConfigShadowColor("clBlack");
        vboxCorChip.setBoxShadowConfigOpacity(75);
        hboxCorChip.addChildren(vboxCorChip);
        vboxCorChip.applyProperties();
    }

    public TFLabel FLabel41 = new TFLabel();

    private void init_FLabel41() {
        FLabel41.setName("FLabel41");
        FLabel41.setLeft(0);
        FLabel41.setTop(0);
        FLabel41.setWidth(56);
        FLabel41.setHeight(13);
        FLabel41.setCaption("Cor do Chip");
        FLabel41.setFontColor("clWindowText");
        FLabel41.setFontSize(-11);
        FLabel41.setFontName("Tahoma");
        FLabel41.setFontStyle("[]");
        FLabel41.setVerticalAlignment("taVerticalCenter");
        FLabel41.setWordBreak(false);
        vboxCorChip.addChildren(FLabel41);
        FLabel41.applyProperties();
    }

    public TFCombo cbCorChip = new TFCombo();

    private void init_cbCorChip() {
        cbCorChip.setName("cbCorChip");
        cbCorChip.setLeft(0);
        cbCorChip.setTop(14);
        cbCorChip.setWidth(200);
        cbCorChip.setHeight(21);
        cbCorChip.setLookupTable(tbComboClassificacaoServ);
        cbCorChip.setLookupKey("ID");
        cbCorChip.setLookupDesc("MONTADORA_CLASSIFICACAO");
        cbCorChip.setFlex(true);
        cbCorChip.setReadOnly(true);
        cbCorChip.setRequired(false);
        cbCorChip.setPrompt("Selecione");
        cbCorChip.setConstraintCheckWhen("cwImmediate");
        cbCorChip.setConstraintCheckType("ctExpression");
        cbCorChip.setConstraintFocusOnError(false);
        cbCorChip.setConstraintEnableUI(true);
        cbCorChip.setConstraintEnabled(false);
        cbCorChip.setConstraintFormCheck(true);
        cbCorChip.setClearOnDelKey(true);
        cbCorChip.setUseClearButton(true);
        cbCorChip.setHideClearButtonOnNullValue(false);
        cbCorChip.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbCorChipChange(event);
            processarFlow("FrmServicos", "cbCorChip", "OnChange");
        });
        cbCorChip.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbCorChipClearClick(event);
            processarFlow("FrmServicos", "cbCorChip", "OnClearClick");
        });
        vboxCorChip.addChildren(cbCorChip);
        cbCorChip.applyProperties();
        addValidatable(cbCorChip);
    }

    public TFPageControl pageControlServico = new TFPageControl();

    private void init_pageControlServico() {
        pageControlServico.setName("pageControlServico");
        pageControlServico.setLeft(0);
        pageControlServico.setTop(66);
        pageControlServico.setWidth(554);
        pageControlServico.setHeight(800);
        pageControlServico.setAlign("alCustom");
        pageControlServico.setTabPosition("tpTop");
        pageControlServico.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            pageControlServicoChange(event);
            processarFlow("FrmServicos", "pageControlServico", "OnChange");
        });
        pageControlServico.setFlexVflex("ftTrue");
        pageControlServico.setFlexHflex("ftTrue");
        pageControlServico.setRenderStyle("rsTabbed");
        pageControlServico.applyProperties();
        FVBox1.addChildren(pageControlServico);
    }

    public TFTabsheet tabSheetListagem = new TFTabsheet();

    private void init_tabSheetListagem() {
        tabSheetListagem.setName("tabSheetListagem");
        tabSheetListagem.setCaption("Lista");
        tabSheetListagem.setVisible(true);
        tabSheetListagem.setClosable(false);
        pageControlServico.addChildren(tabSheetListagem);
        tabSheetListagem.applyProperties();
    }

    public TFBorderPanel FBorderPanel1 = new TFBorderPanel();

    private void init_FBorderPanel1() {
        FBorderPanel1.setName("FBorderPanel1");
        FBorderPanel1.setLeft(2);
        FBorderPanel1.setTop(-27);
        FBorderPanel1.setWidth(1019);
        FBorderPanel1.setHeight(572);
        FBorderPanel1.setCenter("FVBoxGridPrincipal");
        FBorderPanel1.setEast("FVBoxGrFiltrosGeral");
        FBorderPanel1.setFlexVflex("ftTrue");
        FBorderPanel1.setFlexHflex("ftTrue");
        FBorderPanel1.setNorthCollapsible(false);
        FBorderPanel1.setNorthSplittable(false);
        FBorderPanel1.setNorthOpen(true);
        FBorderPanel1.setSouthOpen(true);
        FBorderPanel1.setEastOpen(false);
        FBorderPanel1.setWestOpen(true);
        FBorderPanel1.setSouthCollapsible(false);
        FBorderPanel1.setSouthSplittable(false);
        FBorderPanel1.setEastCaption("Filtros (M\u00EDnimo 2)");
        FBorderPanel1.setEastCollapsible(false);
        FBorderPanel1.setEastSplittable(true);
        FBorderPanel1.setWestCollapsible(false);
        FBorderPanel1.setWestSplittable(false);
        FBorderPanel1.setNorthSizePercent(0);
        FBorderPanel1.setSouthSizePercent(0);
        FBorderPanel1.setEastSizePercent(92);
        FBorderPanel1.setWestSizePercent(0);
        tabSheetListagem.addChildren(FBorderPanel1);
        FBorderPanel1.applyProperties();
    }

    public TFVBox FVBoxGridPrincipal = new TFVBox();

    private void init_FVBoxGridPrincipal() {
        FVBoxGridPrincipal.setName("FVBoxGridPrincipal");
        FVBoxGridPrincipal.setLeft(3);
        FVBoxGridPrincipal.setTop(35);
        FVBoxGridPrincipal.setWidth(361);
        FVBoxGridPrincipal.setHeight(503);
        FVBoxGridPrincipal.setBorderStyle("stNone");
        FVBoxGridPrincipal.setPaddingTop(0);
        FVBoxGridPrincipal.setPaddingLeft(0);
        FVBoxGridPrincipal.setPaddingRight(0);
        FVBoxGridPrincipal.setPaddingBottom(0);
        FVBoxGridPrincipal.setMarginTop(5);
        FVBoxGridPrincipal.setMarginLeft(5);
        FVBoxGridPrincipal.setMarginRight(5);
        FVBoxGridPrincipal.setMarginBottom(5);
        FVBoxGridPrincipal.setSpacing(5);
        FVBoxGridPrincipal.setFlexVflex("ftTrue");
        FVBoxGridPrincipal.setFlexHflex("ftTrue");
        FVBoxGridPrincipal.setScrollable(false);
        FVBoxGridPrincipal.setBoxShadowConfigHorizontalLength(10);
        FVBoxGridPrincipal.setBoxShadowConfigVerticalLength(10);
        FVBoxGridPrincipal.setBoxShadowConfigBlurRadius(5);
        FVBoxGridPrincipal.setBoxShadowConfigSpreadRadius(0);
        FVBoxGridPrincipal.setBoxShadowConfigShadowColor("clBlack");
        FVBoxGridPrincipal.setBoxShadowConfigOpacity(75);
        FBorderPanel1.addChildren(FVBoxGridPrincipal);
        FVBoxGridPrincipal.applyProperties();
    }

    public TFGrid gridServicos = new TFGrid();

    private void init_gridServicos() {
        gridServicos.setName("gridServicos");
        gridServicos.setLeft(0);
        gridServicos.setTop(0);
        gridServicos.setWidth(543);
        gridServicos.setHeight(499);
        gridServicos.setAlign("alClient");
        gridServicos.setTable(tbConsultaServico);
        gridServicos.setFlexVflex("ftTrue");
        gridServicos.setFlexHflex("ftTrue");
        gridServicos.setPagingEnabled(true);
        gridServicos.setFrozenColumns(0);
        gridServicos.setShowFooter(false);
        gridServicos.setShowHeader(true);
        gridServicos.setMultiSelection(false);
        gridServicos.setGroupingEnabled(false);
        gridServicos.setGroupingExpanded(false);
        gridServicos.setGroupingShowFooter(false);
        gridServicos.setCrosstabEnabled(false);
        gridServicos.setCrosstabGroupType("cgtConcat");
        gridServicos.setEditionEnabled(false);
        gridServicos.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_SERVICO");
        item0.setTitleCaption("C\u00F3d. Servi\u00E7o");
        item0.setWidth(100);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(true);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridServicos.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("SERVICO");
        item1.setTitleCaption("Servi\u00E7o");
        item1.setWidth(298);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(true);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridServicos.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("SETOR");
        item2.setTitleCaption("Setor");
        item2.setWidth(100);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(true);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridServicos.getColumns().add(item2);
        FVBoxGridPrincipal.addChildren(gridServicos);
        gridServicos.applyProperties();
    }

    public TFVBox FVBoxGrFiltrosGeral = new TFVBox();

    private void init_FVBoxGrFiltrosGeral() {
        FVBoxGrFiltrosGeral.setName("FVBoxGrFiltrosGeral");
        FVBoxGrFiltrosGeral.setLeft(368);
        FVBoxGrFiltrosGeral.setTop(35);
        FVBoxGrFiltrosGeral.setWidth(644);
        FVBoxGrFiltrosGeral.setHeight(501);
        FVBoxGrFiltrosGeral.setBorderStyle("stNone");
        FVBoxGrFiltrosGeral.setPaddingTop(0);
        FVBoxGrFiltrosGeral.setPaddingLeft(5);
        FVBoxGrFiltrosGeral.setPaddingRight(5);
        FVBoxGrFiltrosGeral.setPaddingBottom(0);
        FVBoxGrFiltrosGeral.setMarginTop(0);
        FVBoxGrFiltrosGeral.setMarginLeft(0);
        FVBoxGrFiltrosGeral.setMarginRight(0);
        FVBoxGrFiltrosGeral.setMarginBottom(0);
        FVBoxGrFiltrosGeral.setSpacing(7);
        FVBoxGrFiltrosGeral.setFlexVflex("ftTrue");
        FVBoxGrFiltrosGeral.setFlexHflex("ftTrue");
        FVBoxGrFiltrosGeral.setScrollable(true);
        FVBoxGrFiltrosGeral.setBoxShadowConfigHorizontalLength(10);
        FVBoxGrFiltrosGeral.setBoxShadowConfigVerticalLength(10);
        FVBoxGrFiltrosGeral.setBoxShadowConfigBlurRadius(5);
        FVBoxGrFiltrosGeral.setBoxShadowConfigSpreadRadius(0);
        FVBoxGrFiltrosGeral.setBoxShadowConfigShadowColor("clBlack");
        FVBoxGrFiltrosGeral.setBoxShadowConfigOpacity(75);
        FBorderPanel1.addChildren(FVBoxGrFiltrosGeral);
        FVBoxGrFiltrosGeral.applyProperties();
    }

    public TFHBox FHBoxGrFiltros1 = new TFHBox();

    private void init_FHBoxGrFiltros1() {
        FHBoxGrFiltros1.setName("FHBoxGrFiltros1");
        FHBoxGrFiltros1.setLeft(0);
        FHBoxGrFiltros1.setTop(0);
        FHBoxGrFiltros1.setWidth(402);
        FHBoxGrFiltros1.setHeight(30);
        FHBoxGrFiltros1.setBorderStyle("stNone");
        FHBoxGrFiltros1.setPaddingTop(0);
        FHBoxGrFiltros1.setPaddingLeft(0);
        FHBoxGrFiltros1.setPaddingRight(0);
        FHBoxGrFiltros1.setPaddingBottom(5);
        FHBoxGrFiltros1.setMarginTop(0);
        FHBoxGrFiltros1.setMarginLeft(0);
        FHBoxGrFiltros1.setMarginRight(0);
        FHBoxGrFiltros1.setMarginBottom(0);
        FHBoxGrFiltros1.setSpacing(5);
        FHBoxGrFiltros1.setFlexVflex("ftMin");
        FHBoxGrFiltros1.setFlexHflex("ftTrue");
        FHBoxGrFiltros1.setScrollable(false);
        FHBoxGrFiltros1.setBoxShadowConfigHorizontalLength(10);
        FHBoxGrFiltros1.setBoxShadowConfigVerticalLength(10);
        FHBoxGrFiltros1.setBoxShadowConfigBlurRadius(5);
        FHBoxGrFiltros1.setBoxShadowConfigSpreadRadius(0);
        FHBoxGrFiltros1.setBoxShadowConfigShadowColor("clBlack");
        FHBoxGrFiltros1.setBoxShadowConfigOpacity(75);
        FHBoxGrFiltros1.setVAlign("tvTop");
        FVBoxGrFiltrosGeral.addChildren(FHBoxGrFiltros1);
        FHBoxGrFiltros1.applyProperties();
    }

    public TFCombo cbGrupoFiltro = new TFCombo();

    private void init_cbGrupoFiltro() {
        cbGrupoFiltro.setName("cbGrupoFiltro");
        cbGrupoFiltro.setLeft(0);
        cbGrupoFiltro.setTop(0);
        cbGrupoFiltro.setWidth(145);
        cbGrupoFiltro.setHeight(21);
        cbGrupoFiltro.setLookupTable(tbServicosGrupo);
        cbGrupoFiltro.setLookupKey("COD_GRUPO_SERV");
        cbGrupoFiltro.setLookupDesc("GRUPO");
        cbGrupoFiltro.setFlex(true);
        cbGrupoFiltro.setReadOnly(true);
        cbGrupoFiltro.setRequired(false);
        cbGrupoFiltro.setPrompt("Grupo");
        cbGrupoFiltro.setConstraintCheckWhen("cwImmediate");
        cbGrupoFiltro.setConstraintCheckType("ctExpression");
        cbGrupoFiltro.setConstraintFocusOnError(false);
        cbGrupoFiltro.setConstraintEnableUI(true);
        cbGrupoFiltro.setConstraintEnabled(false);
        cbGrupoFiltro.setConstraintFormCheck(true);
        cbGrupoFiltro.setClearOnDelKey(true);
        cbGrupoFiltro.setUseClearButton(true);
        cbGrupoFiltro.setHideClearButtonOnNullValue(true);
        cbGrupoFiltro.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbGrupoFiltroClearClick(event);
            processarFlow("FrmServicos", "cbGrupoFiltro", "OnClearClick");
        });
        FHBoxGrFiltros1.addChildren(cbGrupoFiltro);
        cbGrupoFiltro.applyProperties();
        addValidatable(cbGrupoFiltro);
    }

    public TFCombo cbSbGrupoFiltro = new TFCombo();

    private void init_cbSbGrupoFiltro() {
        cbSbGrupoFiltro.setName("cbSbGrupoFiltro");
        cbSbGrupoFiltro.setLeft(145);
        cbSbGrupoFiltro.setTop(0);
        cbSbGrupoFiltro.setWidth(145);
        cbSbGrupoFiltro.setHeight(21);
        cbSbGrupoFiltro.setLookupTable(tbServicosSubGrupo);
        cbSbGrupoFiltro.setLookupKey("COD_SUB_GRUPO_SERV");
        cbSbGrupoFiltro.setLookupDesc("SUBGRUPO");
        cbSbGrupoFiltro.setFlex(true);
        cbSbGrupoFiltro.setReadOnly(true);
        cbSbGrupoFiltro.setRequired(false);
        cbSbGrupoFiltro.setPrompt("SubGrupo");
        cbSbGrupoFiltro.setConstraintCheckWhen("cwImmediate");
        cbSbGrupoFiltro.setConstraintCheckType("ctExpression");
        cbSbGrupoFiltro.setConstraintFocusOnError(false);
        cbSbGrupoFiltro.setConstraintEnableUI(true);
        cbSbGrupoFiltro.setConstraintEnabled(false);
        cbSbGrupoFiltro.setConstraintFormCheck(true);
        cbSbGrupoFiltro.setClearOnDelKey(true);
        cbSbGrupoFiltro.setUseClearButton(true);
        cbSbGrupoFiltro.setHideClearButtonOnNullValue(true);
        FHBoxGrFiltros1.addChildren(cbSbGrupoFiltro);
        cbSbGrupoFiltro.applyProperties();
        addValidatable(cbSbGrupoFiltro);
    }

    public TFHBox FHBoxGrFiltros2 = new TFHBox();

    private void init_FHBoxGrFiltros2() {
        FHBoxGrFiltros2.setName("FHBoxGrFiltros2");
        FHBoxGrFiltros2.setLeft(0);
        FHBoxGrFiltros2.setTop(31);
        FHBoxGrFiltros2.setWidth(402);
        FHBoxGrFiltros2.setHeight(30);
        FHBoxGrFiltros2.setBorderStyle("stNone");
        FHBoxGrFiltros2.setPaddingTop(0);
        FHBoxGrFiltros2.setPaddingLeft(0);
        FHBoxGrFiltros2.setPaddingRight(0);
        FHBoxGrFiltros2.setPaddingBottom(5);
        FHBoxGrFiltros2.setMarginTop(0);
        FHBoxGrFiltros2.setMarginLeft(0);
        FHBoxGrFiltros2.setMarginRight(0);
        FHBoxGrFiltros2.setMarginBottom(0);
        FHBoxGrFiltros2.setSpacing(5);
        FHBoxGrFiltros2.setFlexVflex("ftMin");
        FHBoxGrFiltros2.setFlexHflex("ftTrue");
        FHBoxGrFiltros2.setScrollable(false);
        FHBoxGrFiltros2.setBoxShadowConfigHorizontalLength(10);
        FHBoxGrFiltros2.setBoxShadowConfigVerticalLength(10);
        FHBoxGrFiltros2.setBoxShadowConfigBlurRadius(5);
        FHBoxGrFiltros2.setBoxShadowConfigSpreadRadius(0);
        FHBoxGrFiltros2.setBoxShadowConfigShadowColor("clBlack");
        FHBoxGrFiltros2.setBoxShadowConfigOpacity(75);
        FHBoxGrFiltros2.setVAlign("tvTop");
        FVBoxGrFiltrosGeral.addChildren(FHBoxGrFiltros2);
        FHBoxGrFiltros2.applyProperties();
    }

    public TFCombo cbSetorFiltro = new TFCombo();

    private void init_cbSetorFiltro() {
        cbSetorFiltro.setName("cbSetorFiltro");
        cbSetorFiltro.setLeft(0);
        cbSetorFiltro.setTop(0);
        cbSetorFiltro.setWidth(145);
        cbSetorFiltro.setHeight(21);
        cbSetorFiltro.setLookupTable(tbServicosSetores);
        cbSetorFiltro.setLookupKey("COD_SETOR");
        cbSetorFiltro.setLookupDesc("SETOR");
        cbSetorFiltro.setFlex(true);
        cbSetorFiltro.setReadOnly(true);
        cbSetorFiltro.setRequired(false);
        cbSetorFiltro.setPrompt("Setor");
        cbSetorFiltro.setConstraintCheckWhen("cwImmediate");
        cbSetorFiltro.setConstraintCheckType("ctExpression");
        cbSetorFiltro.setConstraintFocusOnError(false);
        cbSetorFiltro.setConstraintEnableUI(true);
        cbSetorFiltro.setConstraintEnabled(false);
        cbSetorFiltro.setConstraintFormCheck(true);
        cbSetorFiltro.setClearOnDelKey(true);
        cbSetorFiltro.setUseClearButton(true);
        cbSetorFiltro.setHideClearButtonOnNullValue(true);
        FHBoxGrFiltros2.addChildren(cbSetorFiltro);
        cbSetorFiltro.applyProperties();
        addValidatable(cbSetorFiltro);
    }

    public TFCombo cbClassifFiltro = new TFCombo();

    private void init_cbClassifFiltro() {
        cbClassifFiltro.setName("cbClassifFiltro");
        cbClassifFiltro.setLeft(145);
        cbClassifFiltro.setTop(0);
        cbClassifFiltro.setWidth(145);
        cbClassifFiltro.setHeight(21);
        cbClassifFiltro.setLookupTable(tbClassificacaoServ);
        cbClassifFiltro.setLookupKey("COD_CLASSIFICACAO");
        cbClassifFiltro.setLookupDesc("MONTADORA_CLASSIFICACAO");
        cbClassifFiltro.setFlex(true);
        cbClassifFiltro.setReadOnly(true);
        cbClassifFiltro.setRequired(false);
        cbClassifFiltro.setPrompt("Classifica\u00E7\u00E3o");
        cbClassifFiltro.setConstraintCheckWhen("cwImmediate");
        cbClassifFiltro.setConstraintCheckType("ctExpression");
        cbClassifFiltro.setConstraintFocusOnError(false);
        cbClassifFiltro.setConstraintEnableUI(true);
        cbClassifFiltro.setConstraintEnabled(false);
        cbClassifFiltro.setConstraintFormCheck(true);
        cbClassifFiltro.setClearOnDelKey(true);
        cbClassifFiltro.setUseClearButton(true);
        cbClassifFiltro.setHideClearButtonOnNullValue(true);
        FHBoxGrFiltros2.addChildren(cbClassifFiltro);
        cbClassifFiltro.applyProperties();
        addValidatable(cbClassifFiltro);
    }

    public TFHBox FHBoxGrFiltros3 = new TFHBox();

    private void init_FHBoxGrFiltros3() {
        FHBoxGrFiltros3.setName("FHBoxGrFiltros3");
        FHBoxGrFiltros3.setLeft(0);
        FHBoxGrFiltros3.setTop(62);
        FHBoxGrFiltros3.setWidth(401);
        FHBoxGrFiltros3.setHeight(30);
        FHBoxGrFiltros3.setBorderStyle("stNone");
        FHBoxGrFiltros3.setPaddingTop(0);
        FHBoxGrFiltros3.setPaddingLeft(0);
        FHBoxGrFiltros3.setPaddingRight(0);
        FHBoxGrFiltros3.setPaddingBottom(5);
        FHBoxGrFiltros3.setMarginTop(0);
        FHBoxGrFiltros3.setMarginLeft(0);
        FHBoxGrFiltros3.setMarginRight(0);
        FHBoxGrFiltros3.setMarginBottom(0);
        FHBoxGrFiltros3.setSpacing(5);
        FHBoxGrFiltros3.setFlexVflex("ftMin");
        FHBoxGrFiltros3.setFlexHflex("ftTrue");
        FHBoxGrFiltros3.setScrollable(false);
        FHBoxGrFiltros3.setBoxShadowConfigHorizontalLength(10);
        FHBoxGrFiltros3.setBoxShadowConfigVerticalLength(10);
        FHBoxGrFiltros3.setBoxShadowConfigBlurRadius(5);
        FHBoxGrFiltros3.setBoxShadowConfigSpreadRadius(0);
        FHBoxGrFiltros3.setBoxShadowConfigShadowColor("clBlack");
        FHBoxGrFiltros3.setBoxShadowConfigOpacity(75);
        FHBoxGrFiltros3.setVAlign("tvTop");
        FVBoxGrFiltrosGeral.addChildren(FHBoxGrFiltros3);
        FHBoxGrFiltros3.applyProperties();
    }

    public TFCombo cbAtivosFiltro = new TFCombo();

    private void init_cbAtivosFiltro() {
        cbAtivosFiltro.setName("cbAtivosFiltro");
        cbAtivosFiltro.setLeft(0);
        cbAtivosFiltro.setTop(0);
        cbAtivosFiltro.setWidth(145);
        cbAtivosFiltro.setHeight(21);
        cbAtivosFiltro.setFlex(true);
        cbAtivosFiltro.setListOptions("Ativo=S;N\u00E3o Ativo=N");
        cbAtivosFiltro.setReadOnly(true);
        cbAtivosFiltro.setRequired(false);
        cbAtivosFiltro.setPrompt("Status");
        cbAtivosFiltro.setConstraintCheckWhen("cwImmediate");
        cbAtivosFiltro.setConstraintCheckType("ctExpression");
        cbAtivosFiltro.setConstraintFocusOnError(false);
        cbAtivosFiltro.setConstraintEnableUI(true);
        cbAtivosFiltro.setConstraintEnabled(false);
        cbAtivosFiltro.setConstraintFormCheck(true);
        cbAtivosFiltro.setClearOnDelKey(true);
        cbAtivosFiltro.setUseClearButton(true);
        cbAtivosFiltro.setHideClearButtonOnNullValue(true);
        FHBoxGrFiltros3.addChildren(cbAtivosFiltro);
        cbAtivosFiltro.applyProperties();
        addValidatable(cbAtivosFiltro);
    }

    public TFCombo cbOriginaisFiltro = new TFCombo();

    private void init_cbOriginaisFiltro() {
        cbOriginaisFiltro.setName("cbOriginaisFiltro");
        cbOriginaisFiltro.setLeft(145);
        cbOriginaisFiltro.setTop(0);
        cbOriginaisFiltro.setWidth(145);
        cbOriginaisFiltro.setHeight(21);
        cbOriginaisFiltro.setFlex(true);
        cbOriginaisFiltro.setListOptions("\u00C9 Original Montadora=S;N\u00E3o Original=N");
        cbOriginaisFiltro.setReadOnly(true);
        cbOriginaisFiltro.setRequired(false);
        cbOriginaisFiltro.setPrompt("Origem");
        cbOriginaisFiltro.setConstraintCheckWhen("cwImmediate");
        cbOriginaisFiltro.setConstraintCheckType("ctExpression");
        cbOriginaisFiltro.setConstraintFocusOnError(false);
        cbOriginaisFiltro.setConstraintEnableUI(true);
        cbOriginaisFiltro.setConstraintEnabled(false);
        cbOriginaisFiltro.setConstraintFormCheck(true);
        cbOriginaisFiltro.setClearOnDelKey(true);
        cbOriginaisFiltro.setUseClearButton(true);
        cbOriginaisFiltro.setHideClearButtonOnNullValue(true);
        FHBoxGrFiltros3.addChildren(cbOriginaisFiltro);
        cbOriginaisFiltro.applyProperties();
        addValidatable(cbOriginaisFiltro);
    }

    public TFHBox FHBoxGrFiltros4 = new TFHBox();

    private void init_FHBoxGrFiltros4() {
        FHBoxGrFiltros4.setName("FHBoxGrFiltros4");
        FHBoxGrFiltros4.setLeft(0);
        FHBoxGrFiltros4.setTop(93);
        FHBoxGrFiltros4.setWidth(401);
        FHBoxGrFiltros4.setHeight(30);
        FHBoxGrFiltros4.setBorderStyle("stNone");
        FHBoxGrFiltros4.setPaddingTop(0);
        FHBoxGrFiltros4.setPaddingLeft(0);
        FHBoxGrFiltros4.setPaddingRight(0);
        FHBoxGrFiltros4.setPaddingBottom(5);
        FHBoxGrFiltros4.setMarginTop(0);
        FHBoxGrFiltros4.setMarginLeft(0);
        FHBoxGrFiltros4.setMarginRight(0);
        FHBoxGrFiltros4.setMarginBottom(0);
        FHBoxGrFiltros4.setSpacing(5);
        FHBoxGrFiltros4.setFlexVflex("ftMin");
        FHBoxGrFiltros4.setFlexHflex("ftTrue");
        FHBoxGrFiltros4.setScrollable(false);
        FHBoxGrFiltros4.setBoxShadowConfigHorizontalLength(10);
        FHBoxGrFiltros4.setBoxShadowConfigVerticalLength(10);
        FHBoxGrFiltros4.setBoxShadowConfigBlurRadius(5);
        FHBoxGrFiltros4.setBoxShadowConfigSpreadRadius(0);
        FHBoxGrFiltros4.setBoxShadowConfigShadowColor("clBlack");
        FHBoxGrFiltros4.setBoxShadowConfigOpacity(75);
        FHBoxGrFiltros4.setVAlign("tvTop");
        FVBoxGrFiltrosGeral.addChildren(FHBoxGrFiltros4);
        FHBoxGrFiltros4.applyProperties();
    }

    public TFCombo cbCobrarFiltro = new TFCombo();

    private void init_cbCobrarFiltro() {
        cbCobrarFiltro.setName("cbCobrarFiltro");
        cbCobrarFiltro.setLeft(0);
        cbCobrarFiltro.setTop(0);
        cbCobrarFiltro.setWidth(145);
        cbCobrarFiltro.setHeight(21);
        cbCobrarFiltro.setFlex(true);
        cbCobrarFiltro.setListOptions("Tempo=T; Pre\u00E7o=P;N\u00E3o Cobrar=Z");
        cbCobrarFiltro.setReadOnly(true);
        cbCobrarFiltro.setRequired(false);
        cbCobrarFiltro.setPrompt("Como Cobrar");
        cbCobrarFiltro.setConstraintCheckWhen("cwImmediate");
        cbCobrarFiltro.setConstraintCheckType("ctExpression");
        cbCobrarFiltro.setConstraintFocusOnError(false);
        cbCobrarFiltro.setConstraintEnableUI(true);
        cbCobrarFiltro.setConstraintEnabled(false);
        cbCobrarFiltro.setConstraintFormCheck(true);
        cbCobrarFiltro.setClearOnDelKey(true);
        cbCobrarFiltro.setUseClearButton(true);
        cbCobrarFiltro.setHideClearButtonOnNullValue(true);
        FHBoxGrFiltros4.addChildren(cbCobrarFiltro);
        cbCobrarFiltro.applyProperties();
        addValidatable(cbCobrarFiltro);
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(124);
        FHBox2.setWidth(530);
        FHBox2.setHeight(58);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(5);
        FHBox2.setFlexVflex("ftMin");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FVBoxGrFiltrosGeral.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox14 = new TFVBox();

    private void init_FVBox14() {
        FVBox14.setName("FVBox14");
        FVBox14.setLeft(0);
        FVBox14.setTop(0);
        FVBox14.setWidth(92);
        FVBox14.setHeight(49);
        FVBox14.setBorderStyle("stNone");
        FVBox14.setPaddingTop(5);
        FVBox14.setPaddingLeft(0);
        FVBox14.setPaddingRight(0);
        FVBox14.setPaddingBottom(0);
        FVBox14.setMarginTop(0);
        FVBox14.setMarginLeft(0);
        FVBox14.setMarginRight(0);
        FVBox14.setMarginBottom(0);
        FVBox14.setSpacing(1);
        FVBox14.setFlexVflex("ftTrue");
        FVBox14.setFlexHflex("ftFalse");
        FVBox14.setScrollable(false);
        FVBox14.setBoxShadowConfigHorizontalLength(10);
        FVBox14.setBoxShadowConfigVerticalLength(10);
        FVBox14.setBoxShadowConfigBlurRadius(5);
        FVBox14.setBoxShadowConfigSpreadRadius(0);
        FVBox14.setBoxShadowConfigShadowColor("clBlack");
        FVBox14.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox14);
        FVBox14.applyProperties();
    }

    public TFHBox FHBox39 = new TFHBox();

    private void init_FHBox39() {
        FHBox39.setName("FHBox39");
        FHBox39.setLeft(0);
        FHBox39.setTop(0);
        FHBox39.setWidth(87);
        FHBox39.setHeight(19);
        FHBox39.setBorderStyle("stNone");
        FHBox39.setPaddingTop(5);
        FHBox39.setPaddingLeft(0);
        FHBox39.setPaddingRight(0);
        FHBox39.setPaddingBottom(0);
        FHBox39.setMarginTop(0);
        FHBox39.setMarginLeft(0);
        FHBox39.setMarginRight(0);
        FHBox39.setMarginBottom(0);
        FHBox39.setSpacing(1);
        FHBox39.setFlexVflex("ftTrue");
        FHBox39.setFlexHflex("ftTrue");
        FHBox39.setScrollable(false);
        FHBox39.setBoxShadowConfigHorizontalLength(10);
        FHBox39.setBoxShadowConfigVerticalLength(10);
        FHBox39.setBoxShadowConfigBlurRadius(5);
        FHBox39.setBoxShadowConfigSpreadRadius(0);
        FHBox39.setBoxShadowConfigShadowColor("clBlack");
        FHBox39.setBoxShadowConfigOpacity(75);
        FHBox39.setVAlign("tvTop");
        FVBox14.addChildren(FHBox39);
        FHBox39.applyProperties();
    }

    public TFLabel FLabel8 = new TFLabel();

    private void init_FLabel8() {
        FLabel8.setName("FLabel8");
        FLabel8.setLeft(0);
        FLabel8.setTop(0);
        FLabel8.setWidth(33);
        FLabel8.setHeight(13);
        FLabel8.setCaption("C\u00F3digo");
        FLabel8.setFontColor("clWindowText");
        FLabel8.setFontSize(-11);
        FLabel8.setFontName("Tahoma");
        FLabel8.setFontStyle("[]");
        FLabel8.setVerticalAlignment("taVerticalCenter");
        FLabel8.setWordBreak(false);
        FHBox39.addChildren(FLabel8);
        FLabel8.applyProperties();
    }

    public TFVBox FVBox75 = new TFVBox();

    private void init_FVBox75() {
        FVBox75.setName("FVBox75");
        FVBox75.setLeft(33);
        FVBox75.setTop(0);
        FVBox75.setWidth(13);
        FVBox75.setHeight(15);
        FVBox75.setBorderStyle("stNone");
        FVBox75.setPaddingTop(0);
        FVBox75.setPaddingLeft(0);
        FVBox75.setPaddingRight(0);
        FVBox75.setPaddingBottom(0);
        FVBox75.setMarginTop(0);
        FVBox75.setMarginLeft(0);
        FVBox75.setMarginRight(0);
        FVBox75.setMarginBottom(0);
        FVBox75.setSpacing(1);
        FVBox75.setFlexVflex("ftFalse");
        FVBox75.setFlexHflex("ftFalse");
        FVBox75.setScrollable(false);
        FVBox75.setBoxShadowConfigHorizontalLength(10);
        FVBox75.setBoxShadowConfigVerticalLength(10);
        FVBox75.setBoxShadowConfigBlurRadius(5);
        FVBox75.setBoxShadowConfigSpreadRadius(0);
        FVBox75.setBoxShadowConfigShadowColor("clBlack");
        FVBox75.setBoxShadowConfigOpacity(75);
        FHBox39.addChildren(FVBox75);
        FVBox75.applyProperties();
    }

    public TFString FStrCodServFiltro = new TFString();

    private void init_FStrCodServFiltro() {
        FStrCodServFiltro.setName("FStrCodServFiltro");
        FStrCodServFiltro.setLeft(0);
        FStrCodServFiltro.setTop(20);
        FStrCodServFiltro.setWidth(87);
        FStrCodServFiltro.setHeight(24);
        FStrCodServFiltro.setFlex(true);
        FStrCodServFiltro.setRequired(false);
        FStrCodServFiltro.setConstraintCheckWhen("cwImmediate");
        FStrCodServFiltro.setConstraintCheckType("ctExpression");
        FStrCodServFiltro.setConstraintFocusOnError(false);
        FStrCodServFiltro.setConstraintEnableUI(true);
        FStrCodServFiltro.setConstraintEnabled(false);
        FStrCodServFiltro.setConstraintFormCheck(true);
        FStrCodServFiltro.setCharCase("ccNormal");
        FStrCodServFiltro.setPwd(false);
        FStrCodServFiltro.setMaxlength(0);
        FStrCodServFiltro.setFontColor("clWindowText");
        FStrCodServFiltro.setFontSize(-13);
        FStrCodServFiltro.setFontName("Tahoma");
        FStrCodServFiltro.setFontStyle("[]");
        FStrCodServFiltro.setSaveLiteralCharacter(false);
        FStrCodServFiltro.applyProperties();
        FVBox14.addChildren(FStrCodServFiltro);
        addValidatable(FStrCodServFiltro);
    }

    public TFVBox FVBox17 = new TFVBox();

    private void init_FVBox17() {
        FVBox17.setName("FVBox17");
        FVBox17.setLeft(92);
        FVBox17.setTop(0);
        FVBox17.setWidth(191);
        FVBox17.setHeight(49);
        FVBox17.setBorderStyle("stNone");
        FVBox17.setPaddingTop(5);
        FVBox17.setPaddingLeft(5);
        FVBox17.setPaddingRight(0);
        FVBox17.setPaddingBottom(0);
        FVBox17.setMarginTop(0);
        FVBox17.setMarginLeft(0);
        FVBox17.setMarginRight(0);
        FVBox17.setMarginBottom(0);
        FVBox17.setSpacing(1);
        FVBox17.setFlexVflex("ftTrue");
        FVBox17.setFlexHflex("ftTrue");
        FVBox17.setScrollable(false);
        FVBox17.setBoxShadowConfigHorizontalLength(10);
        FVBox17.setBoxShadowConfigVerticalLength(10);
        FVBox17.setBoxShadowConfigBlurRadius(5);
        FVBox17.setBoxShadowConfigSpreadRadius(0);
        FVBox17.setBoxShadowConfigShadowColor("clBlack");
        FVBox17.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox17);
        FVBox17.applyProperties();
    }

    public TFHBox FHBox33 = new TFHBox();

    private void init_FHBox33() {
        FHBox33.setName("FHBox33");
        FHBox33.setLeft(0);
        FHBox33.setTop(0);
        FHBox33.setWidth(185);
        FHBox33.setHeight(19);
        FHBox33.setBorderStyle("stNone");
        FHBox33.setPaddingTop(0);
        FHBox33.setPaddingLeft(0);
        FHBox33.setPaddingRight(0);
        FHBox33.setPaddingBottom(0);
        FHBox33.setMarginTop(0);
        FHBox33.setMarginLeft(0);
        FHBox33.setMarginRight(0);
        FHBox33.setMarginBottom(0);
        FHBox33.setSpacing(1);
        FHBox33.setFlexVflex("ftTrue");
        FHBox33.setFlexHflex("ftTrue");
        FHBox33.setScrollable(false);
        FHBox33.setBoxShadowConfigHorizontalLength(10);
        FHBox33.setBoxShadowConfigVerticalLength(10);
        FHBox33.setBoxShadowConfigBlurRadius(5);
        FHBox33.setBoxShadowConfigSpreadRadius(0);
        FHBox33.setBoxShadowConfigShadowColor("clBlack");
        FHBox33.setBoxShadowConfigOpacity(75);
        FHBox33.setVAlign("tvTop");
        FVBox17.addChildren(FHBox33);
        FHBox33.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(63);
        FVBox2.setHeight(15);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(5);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(5);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftMin");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox33.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel FLabel9 = new TFLabel();

    private void init_FLabel9() {
        FLabel9.setName("FLabel9");
        FLabel9.setLeft(0);
        FLabel9.setTop(0);
        FLabel9.setWidth(46);
        FLabel9.setHeight(13);
        FLabel9.setCaption("Descri\u00E7\u00E3o");
        FLabel9.setFontColor("clWindowText");
        FLabel9.setFontSize(-11);
        FLabel9.setFontName("Tahoma");
        FLabel9.setFontStyle("[]");
        FLabel9.setVerticalAlignment("taVerticalCenter");
        FLabel9.setWordBreak(false);
        FVBox2.addChildren(FLabel9);
        FLabel9.applyProperties();
    }

    public TFCheckBox chkLikeDescricao = new TFCheckBox();

    private void init_chkLikeDescricao() {
        chkLikeDescricao.setName("chkLikeDescricao");
        chkLikeDescricao.setLeft(63);
        chkLikeDescricao.setTop(0);
        chkLikeDescricao.setWidth(113);
        chkLikeDescricao.setHeight(17);
        chkLikeDescricao.setCaption("Qualquer Posi\u00E7\u00E3o");
        chkLikeDescricao.setFontColor("clWindowText");
        chkLikeDescricao.setFontSize(-11);
        chkLikeDescricao.setFontName("Tahoma");
        chkLikeDescricao.setFontStyle("[]");
        chkLikeDescricao.setVerticalAlignment("taAlignTop");
        FHBox33.addChildren(chkLikeDescricao);
        chkLikeDescricao.applyProperties();
    }

    public TFString FStrDescFiltro = new TFString();

    private void init_FStrDescFiltro() {
        FStrDescFiltro.setName("FStrDescFiltro");
        FStrDescFiltro.setLeft(0);
        FStrDescFiltro.setTop(20);
        FStrDescFiltro.setWidth(121);
        FStrDescFiltro.setHeight(24);
        FStrDescFiltro.setFlex(true);
        FStrDescFiltro.setRequired(false);
        FStrDescFiltro.setConstraintCheckWhen("cwImmediate");
        FStrDescFiltro.setConstraintCheckType("ctExpression");
        FStrDescFiltro.setConstraintFocusOnError(false);
        FStrDescFiltro.setConstraintEnableUI(true);
        FStrDescFiltro.setConstraintEnabled(false);
        FStrDescFiltro.setConstraintFormCheck(true);
        FStrDescFiltro.setCharCase("ccNormal");
        FStrDescFiltro.setPwd(false);
        FStrDescFiltro.setMaxlength(0);
        FStrDescFiltro.setFontColor("clWindowText");
        FStrDescFiltro.setFontSize(-13);
        FStrDescFiltro.setFontName("Tahoma");
        FStrDescFiltro.setFontStyle("[]");
        FStrDescFiltro.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FStrDescFiltroEnter(event);
            processarFlow("FrmServicos", "FStrDescFiltro", "OnEnter");
        });
        FStrDescFiltro.setSaveLiteralCharacter(false);
        FStrDescFiltro.applyProperties();
        FVBox17.addChildren(FStrDescFiltro);
        addValidatable(FStrDescFiltro);
    }

    public TFVBox FVBox70 = new TFVBox();

    private void init_FVBox70() {
        FVBox70.setName("FVBox70");
        FVBox70.setLeft(283);
        FVBox70.setTop(0);
        FVBox70.setWidth(110);
        FVBox70.setHeight(49);
        FVBox70.setBorderStyle("stNone");
        FVBox70.setPaddingTop(5);
        FVBox70.setPaddingLeft(0);
        FVBox70.setPaddingRight(0);
        FVBox70.setPaddingBottom(0);
        FVBox70.setMarginTop(0);
        FVBox70.setMarginLeft(0);
        FVBox70.setMarginRight(0);
        FVBox70.setMarginBottom(0);
        FVBox70.setSpacing(1);
        FVBox70.setFlexVflex("ftTrue");
        FVBox70.setFlexHflex("ftFalse");
        FVBox70.setScrollable(false);
        FVBox70.setBoxShadowConfigHorizontalLength(10);
        FVBox70.setBoxShadowConfigVerticalLength(10);
        FVBox70.setBoxShadowConfigBlurRadius(5);
        FVBox70.setBoxShadowConfigSpreadRadius(0);
        FVBox70.setBoxShadowConfigShadowColor("clBlack");
        FVBox70.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox70);
        FVBox70.applyProperties();
    }

    public TFHBox FHBox38 = new TFHBox();

    private void init_FHBox38() {
        FHBox38.setName("FHBox38");
        FHBox38.setLeft(0);
        FHBox38.setTop(0);
        FHBox38.setWidth(105);
        FHBox38.setHeight(19);
        FHBox38.setBorderStyle("stNone");
        FHBox38.setPaddingTop(5);
        FHBox38.setPaddingLeft(0);
        FHBox38.setPaddingRight(0);
        FHBox38.setPaddingBottom(0);
        FHBox38.setMarginTop(0);
        FHBox38.setMarginLeft(0);
        FHBox38.setMarginRight(0);
        FHBox38.setMarginBottom(0);
        FHBox38.setSpacing(1);
        FHBox38.setFlexVflex("ftTrue");
        FHBox38.setFlexHflex("ftTrue");
        FHBox38.setScrollable(false);
        FHBox38.setBoxShadowConfigHorizontalLength(10);
        FHBox38.setBoxShadowConfigVerticalLength(10);
        FHBox38.setBoxShadowConfigBlurRadius(5);
        FHBox38.setBoxShadowConfigSpreadRadius(0);
        FHBox38.setBoxShadowConfigShadowColor("clBlack");
        FHBox38.setBoxShadowConfigOpacity(75);
        FHBox38.setVAlign("tvTop");
        FVBox70.addChildren(FHBox38);
        FHBox38.applyProperties();
    }

    public TFLabel lblOSFiltro = new TFLabel();

    private void init_lblOSFiltro() {
        lblOSFiltro.setName("lblOSFiltro");
        lblOSFiltro.setLeft(0);
        lblOSFiltro.setTop(0);
        lblOSFiltro.setWidth(77);
        lblOSFiltro.setHeight(13);
        lblOSFiltro.setCaption("N\u00FAmero da O.S.");
        lblOSFiltro.setFontColor("clWindowText");
        lblOSFiltro.setFontSize(-11);
        lblOSFiltro.setFontName("Tahoma");
        lblOSFiltro.setFontStyle("[]");
        lblOSFiltro.setVerticalAlignment("taVerticalCenter");
        lblOSFiltro.setWordBreak(false);
        FHBox38.addChildren(lblOSFiltro);
        lblOSFiltro.applyProperties();
    }

    public TFVBox FVBox74 = new TFVBox();

    private void init_FVBox74() {
        FVBox74.setName("FVBox74");
        FVBox74.setLeft(77);
        FVBox74.setTop(0);
        FVBox74.setWidth(13);
        FVBox74.setHeight(15);
        FVBox74.setBorderStyle("stNone");
        FVBox74.setPaddingTop(0);
        FVBox74.setPaddingLeft(0);
        FVBox74.setPaddingRight(0);
        FVBox74.setPaddingBottom(0);
        FVBox74.setMarginTop(0);
        FVBox74.setMarginLeft(0);
        FVBox74.setMarginRight(0);
        FVBox74.setMarginBottom(0);
        FVBox74.setSpacing(1);
        FVBox74.setFlexVflex("ftFalse");
        FVBox74.setFlexHflex("ftFalse");
        FVBox74.setScrollable(false);
        FVBox74.setBoxShadowConfigHorizontalLength(10);
        FVBox74.setBoxShadowConfigVerticalLength(10);
        FVBox74.setBoxShadowConfigBlurRadius(5);
        FVBox74.setBoxShadowConfigSpreadRadius(0);
        FVBox74.setBoxShadowConfigShadowColor("clBlack");
        FVBox74.setBoxShadowConfigOpacity(75);
        FHBox38.addChildren(FVBox74);
        FVBox74.applyProperties();
    }

    public TFInteger nbNumeroOS = new TFInteger();

    private void init_nbNumeroOS() {
        nbNumeroOS.setName("nbNumeroOS");
        nbNumeroOS.setLeft(0);
        nbNumeroOS.setTop(20);
        nbNumeroOS.setWidth(105);
        nbNumeroOS.setHeight(24);
        nbNumeroOS.setFlex(true);
        nbNumeroOS.setRequired(false);
        nbNumeroOS.setConstraintCheckWhen("cwImmediate");
        nbNumeroOS.setConstraintCheckType("ctExpression");
        nbNumeroOS.setConstraintFocusOnError(false);
        nbNumeroOS.setConstraintEnableUI(true);
        nbNumeroOS.setConstraintEnabled(false);
        nbNumeroOS.setConstraintFormCheck(true);
        nbNumeroOS.setMaxlength(0);
        nbNumeroOS.setFontColor("clWindowText");
        nbNumeroOS.setFontSize(-13);
        nbNumeroOS.setFontName("Tahoma");
        nbNumeroOS.setFontStyle("[]");
        nbNumeroOS.setAlignment("taRightJustify");
        FVBox70.addChildren(nbNumeroOS);
        nbNumeroOS.applyProperties();
        addValidatable(nbNumeroOS);
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(183);
        FVBox3.setWidth(554);
        FVBox3.setHeight(260);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(5);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(true);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FVBoxGrFiltrosGeral.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFLabel lblFlagsFiltros = new TFLabel();

    private void init_lblFlagsFiltros() {
        lblFlagsFiltros.setName("lblFlagsFiltros");
        lblFlagsFiltros.setLeft(0);
        lblFlagsFiltros.setTop(0);
        lblFlagsFiltros.setWidth(71);
        lblFlagsFiltros.setHeight(13);
        lblFlagsFiltros.setCaption("Flags Especiais");
        lblFlagsFiltros.setColor("clBtnFace");
        lblFlagsFiltros.setFontColor("clRed");
        lblFlagsFiltros.setFontSize(-11);
        lblFlagsFiltros.setFontName("Tahoma");
        lblFlagsFiltros.setFontStyle("[]");
        lblFlagsFiltros.setVerticalAlignment("taVerticalCenter");
        lblFlagsFiltros.setWordBreak(false);
        FVBox3.addChildren(lblFlagsFiltros);
        lblFlagsFiltros.applyProperties();
    }

    public TFHBox v = new TFHBox();

    private void init_v() {
        v.setName("v");
        v.setLeft(0);
        v.setTop(14);
        v.setWidth(437);
        v.setHeight(22);
        v.setBorderStyle("stNone");
        v.setPaddingTop(0);
        v.setPaddingLeft(0);
        v.setPaddingRight(0);
        v.setPaddingBottom(0);
        v.setMarginTop(0);
        v.setMarginLeft(3);
        v.setMarginRight(0);
        v.setMarginBottom(0);
        v.setSpacing(5);
        v.setFlexVflex("ftMin");
        v.setFlexHflex("ftTrue");
        v.setScrollable(false);
        v.setBoxShadowConfigHorizontalLength(10);
        v.setBoxShadowConfigVerticalLength(10);
        v.setBoxShadowConfigBlurRadius(5);
        v.setBoxShadowConfigSpreadRadius(0);
        v.setBoxShadowConfigShadowColor("clBlack");
        v.setBoxShadowConfigOpacity(75);
        v.setVAlign("tvTop");
        FVBox3.addChildren(v);
        v.applyProperties();
    }

    public TFCheckBox ckbTerceiros = new TFCheckBox();

    private void init_ckbTerceiros() {
        ckbTerceiros.setName("ckbTerceiros");
        ckbTerceiros.setLeft(0);
        ckbTerceiros.setTop(0);
        ckbTerceiros.setWidth(73);
        ckbTerceiros.setHeight(17);
        ckbTerceiros.setCaption("Terceiros");
        ckbTerceiros.setFontColor("clWindowText");
        ckbTerceiros.setFontSize(-11);
        ckbTerceiros.setFontName("Tahoma");
        ckbTerceiros.setFontStyle("[]");
        ckbTerceiros.setVerticalAlignment("taAlignTop");
        v.addChildren(ckbTerceiros);
        ckbTerceiros.applyProperties();
    }

    public TFCheckBox ckbTrocaOleo = new TFCheckBox();

    private void init_ckbTrocaOleo() {
        ckbTrocaOleo.setName("ckbTrocaOleo");
        ckbTrocaOleo.setLeft(73);
        ckbTrocaOleo.setTop(0);
        ckbTrocaOleo.setWidth(93);
        ckbTrocaOleo.setHeight(17);
        ckbTrocaOleo.setCaption("Troca de oleo");
        ckbTrocaOleo.setFontColor("clWindowText");
        ckbTrocaOleo.setFontSize(-11);
        ckbTrocaOleo.setFontName("Tahoma");
        ckbTrocaOleo.setFontStyle("[]");
        ckbTrocaOleo.setVerticalAlignment("taAlignTop");
        v.addChildren(ckbTrocaOleo);
        ckbTrocaOleo.applyProperties();
    }

    public TFCheckBox ckbLubrificacao = new TFCheckBox();

    private void init_ckbLubrificacao() {
        ckbLubrificacao.setName("ckbLubrificacao");
        ckbLubrificacao.setLeft(166);
        ckbLubrificacao.setTop(0);
        ckbLubrificacao.setWidth(81);
        ckbLubrificacao.setHeight(17);
        ckbLubrificacao.setCaption("Lubrifica\u00E7\u00E3o");
        ckbLubrificacao.setFontColor("clWindowText");
        ckbLubrificacao.setFontSize(-11);
        ckbLubrificacao.setFontName("Tahoma");
        ckbLubrificacao.setFontStyle("[]");
        ckbLubrificacao.setVerticalAlignment("taAlignTop");
        v.addChildren(ckbLubrificacao);
        ckbLubrificacao.applyProperties();
    }

    public TFCheckBox ckbLavagem = new TFCheckBox();

    private void init_ckbLavagem() {
        ckbLavagem.setName("ckbLavagem");
        ckbLavagem.setLeft(247);
        ckbLavagem.setTop(0);
        ckbLavagem.setWidth(70);
        ckbLavagem.setHeight(17);
        ckbLavagem.setCaption("Lavagem");
        ckbLavagem.setFontColor("clWindowText");
        ckbLavagem.setFontSize(-11);
        ckbLavagem.setFontName("Tahoma");
        ckbLavagem.setFontStyle("[]");
        ckbLavagem.setVerticalAlignment("taAlignTop");
        v.addChildren(ckbLavagem);
        ckbLavagem.applyProperties();
    }

    public TFCheckBox ckbServicoTZero = new TFCheckBox();

    private void init_ckbServicoTZero() {
        ckbServicoTZero.setName("ckbServicoTZero");
        ckbServicoTZero.setLeft(317);
        ckbServicoTZero.setTop(0);
        ckbServicoTZero.setWidth(121);
        ckbServicoTZero.setHeight(17);
        ckbServicoTZero.setCaption("Servi\u00E7o tempo ZERO");
        ckbServicoTZero.setFontColor("clWindowText");
        ckbServicoTZero.setFontSize(-11);
        ckbServicoTZero.setFontName("Tahoma");
        ckbServicoTZero.setFontStyle("[]");
        ckbServicoTZero.setVerticalAlignment("taAlignTop");
        v.addChildren(ckbServicoTZero);
        ckbServicoTZero.applyProperties();
    }

    public TFLabel lblFlagCruzamentos = new TFLabel();

    private void init_lblFlagCruzamentos() {
        lblFlagCruzamentos.setName("lblFlagCruzamentos");
        lblFlagCruzamentos.setLeft(0);
        lblFlagCruzamentos.setTop(37);
        lblFlagCruzamentos.setWidth(91);
        lblFlagCruzamentos.setHeight(13);
        lblFlagCruzamentos.setCaption("Flags Cruzamentos");
        lblFlagCruzamentos.setFontColor("clRed");
        lblFlagCruzamentos.setFontSize(-11);
        lblFlagCruzamentos.setFontName("Tahoma");
        lblFlagCruzamentos.setFontStyle("[]");
        lblFlagCruzamentos.setVerticalAlignment("taVerticalCenter");
        lblFlagCruzamentos.setWordBreak(false);
        FVBox3.addChildren(lblFlagCruzamentos);
        lblFlagCruzamentos.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(51);
        FHBox4.setWidth(397);
        FHBox4.setHeight(27);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(3);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(5);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FVBox3.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFCheckBox ckbMarca = new TFCheckBox();

    private void init_ckbMarca() {
        ckbMarca.setName("ckbMarca");
        ckbMarca.setLeft(0);
        ckbMarca.setTop(0);
        ckbMarca.setWidth(97);
        ckbMarca.setHeight(17);
        ckbMarca.setCaption("Tem Marca");
        ckbMarca.setFontColor("clWindowText");
        ckbMarca.setFontSize(-11);
        ckbMarca.setFontName("Tahoma");
        ckbMarca.setFontStyle("[]");
        ckbMarca.setVerticalAlignment("taAlignTop");
        FHBox4.addChildren(ckbMarca);
        ckbMarca.applyProperties();
    }

    public TFCheckBox ckbAdicionais = new TFCheckBox();

    private void init_ckbAdicionais() {
        ckbAdicionais.setName("ckbAdicionais");
        ckbAdicionais.setLeft(97);
        ckbAdicionais.setTop(0);
        ckbAdicionais.setWidth(97);
        ckbAdicionais.setHeight(17);
        ckbAdicionais.setCaption("Tem Adicionais");
        ckbAdicionais.setFontColor("clWindowText");
        ckbAdicionais.setFontSize(-11);
        ckbAdicionais.setFontName("Tahoma");
        ckbAdicionais.setFontStyle("[]");
        ckbAdicionais.setVerticalAlignment("taAlignTop");
        FHBox4.addChildren(ckbAdicionais);
        ckbAdicionais.applyProperties();
    }

    public TFCheckBox ckbTMO = new TFCheckBox();

    private void init_ckbTMO() {
        ckbTMO.setName("ckbTMO");
        ckbTMO.setLeft(194);
        ckbTMO.setTop(0);
        ckbTMO.setWidth(147);
        ckbTMO.setHeight(17);
        ckbTMO.setCaption("Sem TMO por Modelo");
        ckbTMO.setFontColor("clWindowText");
        ckbTMO.setFontSize(-11);
        ckbTMO.setFontName("Tahoma");
        ckbTMO.setFontStyle("[]");
        ckbTMO.setVerticalAlignment("taAlignTop");
        FHBox4.addChildren(ckbTMO);
        ckbTMO.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(79);
        FHBox6.setWidth(398);
        FHBox6.setHeight(49);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(3);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(5);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FVBox3.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFVBox FVBox37 = new TFVBox();

    private void init_FVBox37() {
        FVBox37.setName("FVBox37");
        FVBox37.setLeft(0);
        FVBox37.setTop(0);
        FVBox37.setWidth(178);
        FVBox37.setHeight(45);
        FVBox37.setBorderStyle("stNone");
        FVBox37.setPaddingTop(0);
        FVBox37.setPaddingLeft(0);
        FVBox37.setPaddingRight(0);
        FVBox37.setPaddingBottom(0);
        FVBox37.setMarginTop(0);
        FVBox37.setMarginLeft(0);
        FVBox37.setMarginRight(0);
        FVBox37.setMarginBottom(0);
        FVBox37.setSpacing(5);
        FVBox37.setFlexVflex("ftTrue");
        FVBox37.setFlexHflex("ftTrue");
        FVBox37.setScrollable(false);
        FVBox37.setBoxShadowConfigHorizontalLength(10);
        FVBox37.setBoxShadowConfigVerticalLength(10);
        FVBox37.setBoxShadowConfigBlurRadius(5);
        FVBox37.setBoxShadowConfigSpreadRadius(0);
        FVBox37.setBoxShadowConfigShadowColor("clBlack");
        FVBox37.setBoxShadowConfigOpacity(75);
        FHBox6.addChildren(FVBox37);
        FVBox37.applyProperties();
    }

    public TFLabel lblFlagApontamento = new TFLabel();

    private void init_lblFlagApontamento() {
        lblFlagApontamento.setName("lblFlagApontamento");
        lblFlagApontamento.setLeft(0);
        lblFlagApontamento.setTop(0);
        lblFlagApontamento.setWidth(93);
        lblFlagApontamento.setHeight(13);
        lblFlagApontamento.setCaption("Flags Apontamento");
        lblFlagApontamento.setFontColor("clRed");
        lblFlagApontamento.setFontSize(-11);
        lblFlagApontamento.setFontName("Tahoma");
        lblFlagApontamento.setFontStyle("[]");
        lblFlagApontamento.setVerticalAlignment("taVerticalCenter");
        lblFlagApontamento.setWordBreak(false);
        FVBox37.addChildren(lblFlagApontamento);
        lblFlagApontamento.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(14);
        FHBox5.setWidth(172);
        FHBox5.setHeight(33);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(5);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FVBox37.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFCheckBox ckbNPermitir = new TFCheckBox();

    private void init_ckbNPermitir() {
        ckbNPermitir.setName("ckbNPermitir");
        ckbNPermitir.setLeft(0);
        ckbNPermitir.setTop(0);
        ckbNPermitir.setWidth(80);
        ckbNPermitir.setHeight(17);
        ckbNPermitir.setCaption("N\u00E3o Permitir");
        ckbNPermitir.setFontColor("clWindowText");
        ckbNPermitir.setFontSize(-11);
        ckbNPermitir.setFontName("Tahoma");
        ckbNPermitir.setFontStyle("[]");
        ckbNPermitir.setVerticalAlignment("taAlignTop");
        FHBox5.addChildren(ckbNPermitir);
        ckbNPermitir.applyProperties();
    }

    public TFCheckBox ckbForcar = new TFCheckBox();

    private void init_ckbForcar() {
        ckbForcar.setName("ckbForcar");
        ckbForcar.setLeft(80);
        ckbForcar.setTop(0);
        ckbForcar.setWidth(82);
        ckbForcar.setHeight(17);
        ckbForcar.setCaption("N\u00E3o for\u00E7ar");
        ckbForcar.setFontColor("clWindowText");
        ckbForcar.setFontSize(-11);
        ckbForcar.setFontName("Tahoma");
        ckbForcar.setFontStyle("[]");
        ckbForcar.setVerticalAlignment("taAlignTop");
        FHBox5.addChildren(ckbForcar);
        ckbForcar.applyProperties();
    }

    public TFVBox FVBox56 = new TFVBox();

    private void init_FVBox56() {
        FVBox56.setName("FVBox56");
        FVBox56.setLeft(178);
        FVBox56.setTop(0);
        FVBox56.setWidth(217);
        FVBox56.setHeight(45);
        FVBox56.setBorderStyle("stNone");
        FVBox56.setPaddingTop(0);
        FVBox56.setPaddingLeft(0);
        FVBox56.setPaddingRight(0);
        FVBox56.setPaddingBottom(0);
        FVBox56.setMarginTop(0);
        FVBox56.setMarginLeft(0);
        FVBox56.setMarginRight(0);
        FVBox56.setMarginBottom(0);
        FVBox56.setSpacing(5);
        FVBox56.setFlexVflex("ftTrue");
        FVBox56.setFlexHflex("ftTrue");
        FVBox56.setScrollable(false);
        FVBox56.setBoxShadowConfigHorizontalLength(10);
        FVBox56.setBoxShadowConfigVerticalLength(10);
        FVBox56.setBoxShadowConfigBlurRadius(5);
        FVBox56.setBoxShadowConfigSpreadRadius(0);
        FVBox56.setBoxShadowConfigShadowColor("clBlack");
        FVBox56.setBoxShadowConfigOpacity(75);
        FHBox6.addChildren(FVBox56);
        FVBox56.applyProperties();
    }

    public TFLabel lblNfRemessa = new TFLabel();

    private void init_lblNfRemessa() {
        lblNfRemessa.setName("lblNfRemessa");
        lblNfRemessa.setLeft(0);
        lblNfRemessa.setTop(0);
        lblNfRemessa.setWidth(117);
        lblNfRemessa.setHeight(13);
        lblNfRemessa.setCaption("Flags de NF de Remessa");
        lblNfRemessa.setFontColor("clRed");
        lblNfRemessa.setFontSize(-11);
        lblNfRemessa.setFontName("Tahoma");
        lblNfRemessa.setFontStyle("[]");
        lblNfRemessa.setVerticalAlignment("taVerticalCenter");
        lblNfRemessa.setWordBreak(false);
        FVBox56.addChildren(lblNfRemessa);
        lblNfRemessa.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(14);
        FHBox10.setWidth(210);
        FHBox10.setHeight(30);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(5);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftTrue");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FVBox56.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFCheckBox ckbRemessa = new TFCheckBox();

    private void init_ckbRemessa() {
        ckbRemessa.setName("ckbRemessa");
        ckbRemessa.setLeft(0);
        ckbRemessa.setTop(0);
        ckbRemessa.setWidth(94);
        ckbRemessa.setHeight(17);
        ckbRemessa.setHint("Terceiro Remessa");
        ckbRemessa.setCaption("Terceiros Rem.");
        ckbRemessa.setFontColor("clWindowText");
        ckbRemessa.setFontSize(-11);
        ckbRemessa.setFontName("Tahoma");
        ckbRemessa.setFontStyle("[]");
        ckbRemessa.setVerticalAlignment("taAlignTop");
        FHBox10.addChildren(ckbRemessa);
        ckbRemessa.applyProperties();
    }

    public TFCheckBox ckbPassRemessa = new TFCheckBox();

    private void init_ckbPassRemessa() {
        ckbPassRemessa.setName("ckbPassRemessa");
        ckbPassRemessa.setLeft(94);
        ckbPassRemessa.setTop(0);
        ckbPassRemessa.setWidth(90);
        ckbPassRemessa.setHeight(17);
        ckbPassRemessa.setHint("Passivel de Remessa");
        ckbPassRemessa.setCaption("Passivel Rem.");
        ckbPassRemessa.setFontColor("clWindowText");
        ckbPassRemessa.setFontSize(-11);
        ckbPassRemessa.setFontName("Tahoma");
        ckbPassRemessa.setFontStyle("[]");
        ckbPassRemessa.setVerticalAlignment("taAlignTop");
        FHBox10.addChildren(ckbPassRemessa);
        ckbPassRemessa.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(129);
        FHBox8.setWidth(597);
        FHBox8.setHeight(74);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(10);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FVBox3.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(0);
        FVBox4.setTop(0);
        FVBox4.setWidth(272);
        FVBox4.setHeight(73);
        FVBox4.setAlign("alClient");
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(3);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(5);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox8.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(87);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Flags de Impostos");
        FLabel1.setFontColor("clRed");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox4.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(0);
        FHBox19.setTop(14);
        FHBox19.setWidth(268);
        FHBox19.setHeight(54);
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(0);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(0);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(1);
        FHBox19.setFlexVflex("ftTrue");
        FHBox19.setFlexHflex("ftTrue");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvTop");
        FVBox4.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFVBox FVBox29 = new TFVBox();

    private void init_FVBox29() {
        FVBox29.setName("FVBox29");
        FVBox29.setLeft(0);
        FVBox29.setTop(0);
        FVBox29.setWidth(100);
        FVBox29.setHeight(52);
        FVBox29.setBorderStyle("stNone");
        FVBox29.setPaddingTop(0);
        FVBox29.setPaddingLeft(0);
        FVBox29.setPaddingRight(0);
        FVBox29.setPaddingBottom(0);
        FVBox29.setMarginTop(0);
        FVBox29.setMarginLeft(0);
        FVBox29.setMarginRight(0);
        FVBox29.setMarginBottom(0);
        FVBox29.setSpacing(1);
        FVBox29.setFlexVflex("ftFalse");
        FVBox29.setFlexHflex("ftTrue");
        FVBox29.setScrollable(false);
        FVBox29.setBoxShadowConfigHorizontalLength(10);
        FVBox29.setBoxShadowConfigVerticalLength(10);
        FVBox29.setBoxShadowConfigBlurRadius(5);
        FVBox29.setBoxShadowConfigSpreadRadius(0);
        FVBox29.setBoxShadowConfigShadowColor("clBlack");
        FVBox29.setBoxShadowConfigOpacity(75);
        FHBox19.addChildren(FVBox29);
        FVBox29.applyProperties();
    }

    public TFCheckBox ckbRetemIRRF = new TFCheckBox();

    private void init_ckbRetemIRRF() {
        ckbRetemIRRF.setName("ckbRetemIRRF");
        ckbRetemIRRF.setLeft(0);
        ckbRetemIRRF.setTop(0);
        ckbRetemIRRF.setWidth(80);
        ckbRetemIRRF.setHeight(17);
        ckbRetemIRRF.setCaption("Retem IRRF");
        ckbRetemIRRF.setFontColor("clWindowText");
        ckbRetemIRRF.setFontSize(-11);
        ckbRetemIRRF.setFontName("Tahoma");
        ckbRetemIRRF.setFontStyle("[]");
        ckbRetemIRRF.setVerticalAlignment("taAlignTop");
        FVBox29.addChildren(ckbRetemIRRF);
        ckbRetemIRRF.applyProperties();
    }

    public TFCheckBox ckbRetemINSS = new TFCheckBox();

    private void init_ckbRetemINSS() {
        ckbRetemINSS.setName("ckbRetemINSS");
        ckbRetemINSS.setLeft(0);
        ckbRetemINSS.setTop(18);
        ckbRetemINSS.setWidth(85);
        ckbRetemINSS.setHeight(17);
        ckbRetemINSS.setCaption("Retem INSS");
        ckbRetemINSS.setFontColor("clWindowText");
        ckbRetemINSS.setFontSize(-11);
        ckbRetemINSS.setFontName("Tahoma");
        ckbRetemINSS.setFontStyle("[]");
        ckbRetemINSS.setVerticalAlignment("taAlignTop");
        FVBox29.addChildren(ckbRetemINSS);
        ckbRetemINSS.applyProperties();
    }

    public TFVBox FVBox62 = new TFVBox();

    private void init_FVBox62() {
        FVBox62.setName("FVBox62");
        FVBox62.setLeft(100);
        FVBox62.setTop(0);
        FVBox62.setWidth(177);
        FVBox62.setHeight(52);
        FVBox62.setBorderStyle("stNone");
        FVBox62.setPaddingTop(0);
        FVBox62.setPaddingLeft(0);
        FVBox62.setPaddingRight(0);
        FVBox62.setPaddingBottom(0);
        FVBox62.setMarginTop(0);
        FVBox62.setMarginLeft(0);
        FVBox62.setMarginRight(0);
        FVBox62.setMarginBottom(0);
        FVBox62.setSpacing(1);
        FVBox62.setFlexVflex("ftFalse");
        FVBox62.setFlexHflex("ftTrue");
        FVBox62.setScrollable(false);
        FVBox62.setBoxShadowConfigHorizontalLength(10);
        FVBox62.setBoxShadowConfigVerticalLength(10);
        FVBox62.setBoxShadowConfigBlurRadius(5);
        FVBox62.setBoxShadowConfigSpreadRadius(0);
        FVBox62.setBoxShadowConfigShadowColor("clBlack");
        FVBox62.setBoxShadowConfigOpacity(75);
        FHBox19.addChildren(FVBox62);
        FVBox62.applyProperties();
    }

    public TFCheckBox ckbNRetemPccFiltro = new TFCheckBox();

    private void init_ckbNRetemPccFiltro() {
        ckbNRetemPccFiltro.setName("ckbNRetemPccFiltro");
        ckbNRetemPccFiltro.setLeft(0);
        ckbNRetemPccFiltro.setTop(0);
        ckbNRetemPccFiltro.setWidth(96);
        ckbNRetemPccFiltro.setHeight(17);
        ckbNRetemPccFiltro.setCaption("N\u00E3o  Retem PCC");
        ckbNRetemPccFiltro.setFontColor("clWindowText");
        ckbNRetemPccFiltro.setFontSize(-11);
        ckbNRetemPccFiltro.setFontName("Tahoma");
        ckbNRetemPccFiltro.setFontStyle("[]");
        ckbNRetemPccFiltro.setVerticalAlignment("taAlignTop");
        FVBox62.addChildren(ckbNRetemPccFiltro);
        ckbNRetemPccFiltro.applyProperties();
    }

    public TFCheckBox ckbNaoReter = new TFCheckBox();

    private void init_ckbNaoReter() {
        ckbNaoReter.setName("ckbNaoReter");
        ckbNaoReter.setLeft(0);
        ckbNaoReter.setTop(18);
        ckbNaoReter.setWidth(153);
        ckbNaoReter.setHeight(17);
        ckbNaoReter.setCaption("N\u00E3o Reter Pis/Cofins/CSLL");
        ckbNaoReter.setFontColor("clWindowText");
        ckbNaoReter.setFontSize(-11);
        ckbNaoReter.setFontName("Tahoma");
        ckbNaoReter.setFontStyle("[]");
        ckbNaoReter.setVerticalAlignment("taAlignTop");
        FVBox62.addChildren(ckbNaoReter);
        ckbNaoReter.applyProperties();
    }

    public TFVBox FVBox18 = new TFVBox();

    private void init_FVBox18() {
        FVBox18.setName("FVBox18");
        FVBox18.setLeft(272);
        FVBox18.setTop(0);
        FVBox18.setWidth(130);
        FVBox18.setHeight(73);
        FVBox18.setBorderStyle("stNone");
        FVBox18.setPaddingTop(0);
        FVBox18.setPaddingLeft(0);
        FVBox18.setPaddingRight(0);
        FVBox18.setPaddingBottom(0);
        FVBox18.setMarginTop(0);
        FVBox18.setMarginLeft(3);
        FVBox18.setMarginRight(0);
        FVBox18.setMarginBottom(0);
        FVBox18.setSpacing(5);
        FVBox18.setFlexVflex("ftTrue");
        FVBox18.setFlexHflex("ftTrue");
        FVBox18.setScrollable(false);
        FVBox18.setBoxShadowConfigHorizontalLength(10);
        FVBox18.setBoxShadowConfigVerticalLength(10);
        FVBox18.setBoxShadowConfigBlurRadius(5);
        FVBox18.setBoxShadowConfigSpreadRadius(0);
        FVBox18.setBoxShadowConfigShadowColor("clBlack");
        FVBox18.setBoxShadowConfigOpacity(75);
        FHBox8.addChildren(FVBox18);
        FVBox18.applyProperties();
    }

    public TFLabel FLabel38 = new TFLabel();

    private void init_FLabel38() {
        FLabel38.setName("FLabel38");
        FLabel38.setLeft(0);
        FLabel38.setTop(0);
        FLabel38.setWidth(95);
        FLabel38.setHeight(13);
        FLabel38.setCaption("Flags de Montadora");
        FLabel38.setFontColor("clRed");
        FLabel38.setFontSize(-11);
        FLabel38.setFontName("Tahoma");
        FLabel38.setFontStyle("[]");
        FLabel38.setVerticalAlignment("taVerticalCenter");
        FLabel38.setWordBreak(false);
        FVBox18.addChildren(FLabel38);
        FLabel38.applyProperties();
    }

    public TFVBox FVBox81 = new TFVBox();

    private void init_FVBox81() {
        FVBox81.setName("FVBox81");
        FVBox81.setLeft(0);
        FVBox81.setTop(14);
        FVBox81.setWidth(120);
        FVBox81.setHeight(56);
        FVBox81.setAlign("alClient");
        FVBox81.setBorderStyle("stNone");
        FVBox81.setPaddingTop(0);
        FVBox81.setPaddingLeft(0);
        FVBox81.setPaddingRight(0);
        FVBox81.setPaddingBottom(0);
        FVBox81.setMarginTop(0);
        FVBox81.setMarginLeft(0);
        FVBox81.setMarginRight(0);
        FVBox81.setMarginBottom(0);
        FVBox81.setSpacing(1);
        FVBox81.setFlexVflex("ftFalse");
        FVBox81.setFlexHflex("ftTrue");
        FVBox81.setScrollable(false);
        FVBox81.setBoxShadowConfigHorizontalLength(10);
        FVBox81.setBoxShadowConfigVerticalLength(10);
        FVBox81.setBoxShadowConfigBlurRadius(5);
        FVBox81.setBoxShadowConfigSpreadRadius(0);
        FVBox81.setBoxShadowConfigShadowColor("clBlack");
        FVBox81.setBoxShadowConfigOpacity(75);
        FVBox18.addChildren(FVBox81);
        FVBox81.applyProperties();
    }

    public TFCheckBox ckbBmwIspa = new TFCheckBox();

    private void init_ckbBmwIspa() {
        ckbBmwIspa.setName("ckbBmwIspa");
        ckbBmwIspa.setLeft(0);
        ckbBmwIspa.setTop(0);
        ckbBmwIspa.setWidth(97);
        ckbBmwIspa.setHeight(17);
        ckbBmwIspa.setCaption("BMW ISPA");
        ckbBmwIspa.setFontColor("clWindowText");
        ckbBmwIspa.setFontSize(-11);
        ckbBmwIspa.setFontName("Tahoma");
        ckbBmwIspa.setFontStyle("[]");
        ckbBmwIspa.setVerticalAlignment("taAlignTop");
        FVBox81.addChildren(ckbBmwIspa);
        ckbBmwIspa.applyProperties();
    }

    public TFCheckBox ckbAutFabrica = new TFCheckBox();

    private void init_ckbAutFabrica() {
        ckbAutFabrica.setName("ckbAutFabrica");
        ckbAutFabrica.setLeft(0);
        ckbAutFabrica.setTop(18);
        ckbAutFabrica.setWidth(125);
        ckbAutFabrica.setHeight(17);
        ckbAutFabrica.setCaption("Autoriza\u00E7\u00E3o Fabrica");
        ckbAutFabrica.setFontColor("clWindowText");
        ckbAutFabrica.setFontSize(-11);
        ckbAutFabrica.setFontName("Tahoma");
        ckbAutFabrica.setFontStyle("[]");
        ckbAutFabrica.setVerticalAlignment("taAlignTop");
        FVBox81.addChildren(ckbAutFabrica);
        ckbAutFabrica.applyProperties();
    }

    public TFButton BtnLimparFiltros = new TFButton();

    private void init_BtnLimparFiltros() {
        BtnLimparFiltros.setName("BtnLimparFiltros");
        BtnLimparFiltros.setLeft(0);
        BtnLimparFiltros.setTop(204);
        BtnLimparFiltros.setWidth(75);
        BtnLimparFiltros.setHeight(25);
        BtnLimparFiltros.setCaption("Limpar");
        BtnLimparFiltros.setFontColor("clWindowText");
        BtnLimparFiltros.setFontSize(-11);
        BtnLimparFiltros.setFontName("Tahoma");
        BtnLimparFiltros.setFontStyle("[]");
        BtnLimparFiltros.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            BtnLimparFiltrosClick(event);
            processarFlow("FrmServicos", "BtnLimparFiltros", "OnClick");
        });
        BtnLimparFiltros.setImageId(0);
        BtnLimparFiltros.setColor("clBtnFace");
        BtnLimparFiltros.setAccess(false);
        BtnLimparFiltros.setIconReverseDirection(false);
        FVBox3.addChildren(BtnLimparFiltros);
        BtnLimparFiltros.applyProperties();
    }

    public TFTabsheet tabCadastro = new TFTabsheet();

    private void init_tabCadastro() {
        tabCadastro.setName("tabCadastro");
        tabCadastro.setCaption("Cadastro");
        tabCadastro.setVisible(true);
        tabCadastro.setClosable(false);
        pageControlServico.addChildren(tabCadastro);
        tabCadastro.applyProperties();
    }

    public TFVBox vBoxTabCadastro = new TFVBox();

    private void init_vBoxTabCadastro() {
        vBoxTabCadastro.setName("vBoxTabCadastro");
        vBoxTabCadastro.setLeft(0);
        vBoxTabCadastro.setTop(0);
        vBoxTabCadastro.setWidth(546);
        vBoxTabCadastro.setHeight(772);
        vBoxTabCadastro.setAlign("alClient");
        vBoxTabCadastro.setBorderStyle("stNone");
        vBoxTabCadastro.setPaddingTop(0);
        vBoxTabCadastro.setPaddingLeft(0);
        vBoxTabCadastro.setPaddingRight(0);
        vBoxTabCadastro.setPaddingBottom(0);
        vBoxTabCadastro.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vBoxTabCadastroClick(event);
            processarFlow("FrmServicos", "vBoxTabCadastro", "OnClick");
        });
        vBoxTabCadastro.setMarginTop(5);
        vBoxTabCadastro.setMarginLeft(5);
        vBoxTabCadastro.setMarginRight(5);
        vBoxTabCadastro.setMarginBottom(5);
        vBoxTabCadastro.setSpacing(5);
        vBoxTabCadastro.setFlexVflex("ftTrue");
        vBoxTabCadastro.setFlexHflex("ftTrue");
        vBoxTabCadastro.setScrollable(true);
        vBoxTabCadastro.setBoxShadowConfigHorizontalLength(10);
        vBoxTabCadastro.setBoxShadowConfigVerticalLength(10);
        vBoxTabCadastro.setBoxShadowConfigBlurRadius(5);
        vBoxTabCadastro.setBoxShadowConfigSpreadRadius(0);
        vBoxTabCadastro.setBoxShadowConfigShadowColor("clBlack");
        vBoxTabCadastro.setBoxShadowConfigOpacity(75);
        tabCadastro.addChildren(vBoxTabCadastro);
        vBoxTabCadastro.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(1000);
        FHBox1.setHeight(53);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftMin");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxTabCadastro.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(0);
        FVBox6.setTop(0);
        FVBox6.setWidth(186);
        FVBox6.setHeight(53);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftTrue");
        FVBox6.setFlexHflex("ftTrue");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(33);
        FLabel2.setHeight(13);
        FLabel2.setCaption("C\u00F3digo");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox6.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFString FIntCod = new TFString();

    private void init_FIntCod() {
        FIntCod.setName("FIntCod");
        FIntCod.setLeft(0);
        FIntCod.setTop(14);
        FIntCod.setWidth(145);
        FIntCod.setHeight(24);
        FIntCod.setTable(tbServicos);
        FIntCod.setFieldName("COD_SERVICO");
        FIntCod.setFlex(true);
        FIntCod.setRequired(true);
        FIntCod.setConstraintExpression("value is null or trim(value) = ''");
        FIntCod.setConstraintMessage("C\u00F3digo de servi\u00E7o, preenchimento obrigat\u00F3rio");
        FIntCod.setConstraintCheckWhen("cwImmediate");
        FIntCod.setConstraintCheckType("ctExpression");
        FIntCod.setConstraintFocusOnError(false);
        FIntCod.setConstraintEnableUI(true);
        FIntCod.setConstraintEnabled(true);
        FIntCod.setConstraintFormCheck(true);
        FIntCod.setCharCase("ccNormal");
        FIntCod.setPwd(false);
        FIntCod.setMaxlength(15);
        FIntCod.setFontColor("clWindowText");
        FIntCod.setFontSize(-13);
        FIntCod.setFontName("Tahoma");
        FIntCod.setFontStyle("[]");
        FIntCod.setSaveLiteralCharacter(false);
        FIntCod.applyProperties();
        FVBox6.addChildren(FIntCod);
        addValidatable(FIntCod);
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(186);
        FVBox7.setTop(0);
        FVBox7.setWidth(185);
        FVBox7.setHeight(53);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(5);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftTrue");
        FVBox7.setFlexHflex("ftTrue");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(0);
        FLabel3.setTop(0);
        FLabel3.setWidth(51);
        FLabel3.setHeight(13);
        FLabel3.setCaption("Fabricante");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        FVBox7.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFString FStrFabricante = new TFString();

    private void init_FStrFabricante() {
        FStrFabricante.setName("FStrFabricante");
        FStrFabricante.setLeft(0);
        FStrFabricante.setTop(14);
        FStrFabricante.setWidth(160);
        FStrFabricante.setHeight(24);
        FStrFabricante.setTable(tbServicos);
        FStrFabricante.setFieldName("COD_FABRICANTE");
        FStrFabricante.setFlex(true);
        FStrFabricante.setRequired(false);
        FStrFabricante.setConstraintExpression("value < 0");
        FStrFabricante.setConstraintMessage("C\u00F3d. Fabricante n\u00E3o \u00E9 permitdo negativo");
        FStrFabricante.setConstraintCheckWhen("cwImmediate");
        FStrFabricante.setConstraintCheckType("ctExpression");
        FStrFabricante.setConstraintFocusOnError(false);
        FStrFabricante.setConstraintEnableUI(true);
        FStrFabricante.setConstraintEnabled(true);
        FStrFabricante.setConstraintFormCheck(true);
        FStrFabricante.setCharCase("ccNormal");
        FStrFabricante.setPwd(false);
        FStrFabricante.setMaxlength(20);
        FStrFabricante.setFontColor("clWindowText");
        FStrFabricante.setFontSize(-13);
        FStrFabricante.setFontName("Tahoma");
        FStrFabricante.setFontStyle("[]");
        FStrFabricante.setSaveLiteralCharacter(false);
        FStrFabricante.applyProperties();
        FVBox7.addChildren(FStrFabricante);
        addValidatable(FStrFabricante);
    }

    public TFVBox FVBox79 = new TFVBox();

    private void init_FVBox79() {
        FVBox79.setName("FVBox79");
        FVBox79.setLeft(371);
        FVBox79.setTop(0);
        FVBox79.setWidth(104);
        FVBox79.setHeight(52);
        FVBox79.setBorderStyle("stNone");
        FVBox79.setPaddingTop(0);
        FVBox79.setPaddingLeft(0);
        FVBox79.setPaddingRight(0);
        FVBox79.setPaddingBottom(0);
        FVBox79.setMarginTop(0);
        FVBox79.setMarginLeft(0);
        FVBox79.setMarginRight(0);
        FVBox79.setMarginBottom(0);
        FVBox79.setSpacing(1);
        FVBox79.setFlexVflex("ftFalse");
        FVBox79.setFlexHflex("ftFalse");
        FVBox79.setScrollable(false);
        FVBox79.setBoxShadowConfigHorizontalLength(10);
        FVBox79.setBoxShadowConfigVerticalLength(10);
        FVBox79.setBoxShadowConfigBlurRadius(5);
        FVBox79.setBoxShadowConfigSpreadRadius(0);
        FVBox79.setBoxShadowConfigShadowColor("clBlack");
        FVBox79.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox79);
        FVBox79.applyProperties();
    }

    public TFHBox FHBox41 = new TFHBox();

    private void init_FHBox41() {
        FHBox41.setName("FHBox41");
        FHBox41.setLeft(0);
        FHBox41.setTop(0);
        FHBox41.setWidth(56);
        FHBox41.setHeight(21);
        FHBox41.setBorderStyle("stNone");
        FHBox41.setPaddingTop(0);
        FHBox41.setPaddingLeft(0);
        FHBox41.setPaddingRight(0);
        FHBox41.setPaddingBottom(0);
        FHBox41.setMarginTop(0);
        FHBox41.setMarginLeft(0);
        FHBox41.setMarginRight(0);
        FHBox41.setMarginBottom(0);
        FHBox41.setSpacing(1);
        FHBox41.setFlexVflex("ftFalse");
        FHBox41.setFlexHflex("ftFalse");
        FHBox41.setScrollable(false);
        FHBox41.setBoxShadowConfigHorizontalLength(10);
        FHBox41.setBoxShadowConfigVerticalLength(10);
        FHBox41.setBoxShadowConfigBlurRadius(5);
        FHBox41.setBoxShadowConfigSpreadRadius(0);
        FHBox41.setBoxShadowConfigShadowColor("clBlack");
        FHBox41.setBoxShadowConfigOpacity(75);
        FHBox41.setVAlign("tvTop");
        FVBox79.addChildren(FHBox41);
        FHBox41.applyProperties();
    }

    public TFCheckBox chkOriginal = new TFCheckBox();

    private void init_chkOriginal() {
        chkOriginal.setName("chkOriginal");
        chkOriginal.setLeft(0);
        chkOriginal.setTop(22);
        chkOriginal.setWidth(97);
        chkOriginal.setHeight(17);
        chkOriginal.setCaption("Original?");
        chkOriginal.setFontColor("clWindowText");
        chkOriginal.setFontSize(-11);
        chkOriginal.setFontName("Tahoma");
        chkOriginal.setFontStyle("[]");
        chkOriginal.setTable(tbServicos);
        chkOriginal.setFieldName("ORIGINAL");
        chkOriginal.setCheckedValue("S");
        chkOriginal.setUncheckedValue("N");
        chkOriginal.setVerticalAlignment("taAlignTop");
        FVBox79.addChildren(chkOriginal);
        chkOriginal.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(0);
        FLabel6.setTop(54);
        FLabel6.setWidth(46);
        FLabel6.setHeight(13);
        FLabel6.setCaption("Descri\u00E7\u00E3o");
        FLabel6.setFontColor("clWindowText");
        FLabel6.setFontSize(-11);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        vBoxTabCadastro.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFString FStrDesc = new TFString();

    private void init_FStrDesc() {
        FStrDesc.setName("FStrDesc");
        FStrDesc.setLeft(0);
        FStrDesc.setTop(68);
        FStrDesc.setWidth(540);
        FStrDesc.setHeight(24);
        FStrDesc.setTable(tbServicos);
        FStrDesc.setFieldName("DESCRICAO_SERVICO");
        FStrDesc.setFlex(true);
        FStrDesc.setRequired(true);
        FStrDesc.setConstraintExpression("value is null or trim(value) = ''");
        FStrDesc.setConstraintMessage("Descri\u00E7\u00E3o preenchimento obrigat\u00F3rio");
        FStrDesc.setConstraintCheckWhen("cwImmediate");
        FStrDesc.setConstraintCheckType("ctExpression");
        FStrDesc.setConstraintFocusOnError(false);
        FStrDesc.setConstraintGroupName("Descri\u00E7\u00E3o");
        FStrDesc.setConstraintEnableUI(true);
        FStrDesc.setConstraintEnabled(true);
        FStrDesc.setConstraintFormCheck(true);
        FStrDesc.setCharCase("ccNormal");
        FStrDesc.setPwd(false);
        FStrDesc.setMaxlength(200);
        FStrDesc.setFontColor("clWindowText");
        FStrDesc.setFontSize(-13);
        FStrDesc.setFontName("Tahoma");
        FStrDesc.setFontStyle("[]");
        FStrDesc.setSaveLiteralCharacter(false);
        FStrDesc.applyProperties();
        vBoxTabCadastro.addChildren(FStrDesc);
        addValidatable(FStrDesc);
    }

    public TFLabel FLabel7 = new TFLabel();

    private void init_FLabel7() {
        FLabel7.setName("FLabel7");
        FLabel7.setLeft(0);
        FLabel7.setTop(93);
        FLabel7.setWidth(98);
        FLabel7.setHeight(13);
        FLabel7.setCaption("Descri\u00E7\u00E3o Detalhada");
        FLabel7.setFontColor("clWindowText");
        FLabel7.setFontSize(-11);
        FLabel7.setFontName("Tahoma");
        FLabel7.setFontStyle("[]");
        FLabel7.setVerticalAlignment("taVerticalCenter");
        FLabel7.setWordBreak(false);
        vBoxTabCadastro.addChildren(FLabel7);
        FLabel7.applyProperties();
    }

    public TFMemo FMemDetalhe = new TFMemo();

    private void init_FMemDetalhe() {
        FMemDetalhe.setName("FMemDetalhe");
        FMemDetalhe.setLeft(0);
        FMemDetalhe.setTop(107);
        FMemDetalhe.setWidth(540);
        FMemDetalhe.setHeight(70);
        FMemDetalhe.setCharCase("ccNormal");
        FMemDetalhe.setFontColor("clWindowText");
        FMemDetalhe.setFontSize(-11);
        FMemDetalhe.setFontName("Tahoma");
        FMemDetalhe.setFontStyle("[]");
        FMemDetalhe.setMaxlength(2000);
        FMemDetalhe.setFieldName("MAIS_DETALHES");
        FMemDetalhe.setTable(tbServicos);
        FMemDetalhe.setFlexVflex("ftFalse");
        FMemDetalhe.setFlexHflex("ftTrue");
        FMemDetalhe.setConstraintCheckWhen("cwImmediate");
        FMemDetalhe.setConstraintCheckType("ctExpression");
        FMemDetalhe.setConstraintFocusOnError(false);
        FMemDetalhe.setConstraintEnableUI(true);
        FMemDetalhe.setConstraintEnabled(false);
        FMemDetalhe.setConstraintFormCheck(true);
        FMemDetalhe.setRequired(false);
        vBoxTabCadastro.addChildren(FMemDetalhe);
        FMemDetalhe.applyProperties();
        addValidatable(FMemDetalhe);
    }

    public TFString FStrAviso = new TFString();

    private void init_FStrAviso() {
        FStrAviso.setName("FStrAviso");
        FStrAviso.setLeft(0);
        FStrAviso.setTop(178);
        FStrAviso.setWidth(540);
        FStrAviso.setHeight(24);
        FStrAviso.setTable(tbServicos);
        FStrAviso.setFieldName("AVISO");
        FStrAviso.setFlex(true);
        FStrAviso.setRequired(false);
        FStrAviso.setPrompt("Aviso na CODIFICA\u00C7\u00C3O");
        FStrAviso.setConstraintCheckWhen("cwImmediate");
        FStrAviso.setConstraintCheckType("ctExpression");
        FStrAviso.setConstraintFocusOnError(false);
        FStrAviso.setConstraintEnableUI(true);
        FStrAviso.setConstraintEnabled(false);
        FStrAviso.setConstraintFormCheck(true);
        FStrAviso.setCharCase("ccNormal");
        FStrAviso.setPwd(false);
        FStrAviso.setMaxlength(200);
        FStrAviso.setFontColor("clWindowText");
        FStrAviso.setFontSize(-13);
        FStrAviso.setFontName("Tahoma");
        FStrAviso.setFontStyle("[]");
        FStrAviso.setSaveLiteralCharacter(false);
        FStrAviso.applyProperties();
        vBoxTabCadastro.addChildren(FStrAviso);
        addValidatable(FStrAviso);
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(203);
        FHBox11.setWidth(540);
        FHBox11.setHeight(45);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(5);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftMin");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        vBoxTabCadastro.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFVBox FVBox57 = new TFVBox();

    private void init_FVBox57() {
        FVBox57.setName("FVBox57");
        FVBox57.setLeft(0);
        FVBox57.setTop(0);
        FVBox57.setWidth(185);
        FVBox57.setHeight(41);
        FVBox57.setBorderStyle("stNone");
        FVBox57.setPaddingTop(0);
        FVBox57.setPaddingLeft(0);
        FVBox57.setPaddingRight(0);
        FVBox57.setPaddingBottom(0);
        FVBox57.setMarginTop(0);
        FVBox57.setMarginLeft(0);
        FVBox57.setMarginRight(0);
        FVBox57.setMarginBottom(0);
        FVBox57.setSpacing(1);
        FVBox57.setFlexVflex("ftMin");
        FVBox57.setFlexHflex("ftTrue");
        FVBox57.setScrollable(false);
        FVBox57.setBoxShadowConfigHorizontalLength(10);
        FVBox57.setBoxShadowConfigVerticalLength(10);
        FVBox57.setBoxShadowConfigBlurRadius(5);
        FVBox57.setBoxShadowConfigSpreadRadius(0);
        FVBox57.setBoxShadowConfigShadowColor("clBlack");
        FVBox57.setBoxShadowConfigOpacity(75);
        FHBox11.addChildren(FVBox57);
        FVBox57.applyProperties();
    }

    public TFLabel FLabel17 = new TFLabel();

    private void init_FLabel17() {
        FLabel17.setName("FLabel17");
        FLabel17.setLeft(0);
        FLabel17.setTop(0);
        FLabel17.setWidth(29);
        FLabel17.setHeight(13);
        FLabel17.setCaption("Grupo");
        FLabel17.setFontColor("clWindowText");
        FLabel17.setFontSize(-11);
        FLabel17.setFontName("Tahoma");
        FLabel17.setFontStyle("[]");
        FLabel17.setVerticalAlignment("taVerticalCenter");
        FLabel17.setWordBreak(false);
        FVBox57.addChildren(FLabel17);
        FLabel17.applyProperties();
    }

    public TFCombo FCoGrupoCad = new TFCombo();

    private void init_FCoGrupoCad() {
        FCoGrupoCad.setName("FCoGrupoCad");
        FCoGrupoCad.setLeft(0);
        FCoGrupoCad.setTop(14);
        FCoGrupoCad.setWidth(145);
        FCoGrupoCad.setHeight(21);
        FCoGrupoCad.setTable(tbServicos);
        FCoGrupoCad.setLookupTable(tbServicosGrupoCadastro);
        FCoGrupoCad.setFieldName("COD_GRUPO_SERV");
        FCoGrupoCad.setLookupKey("COD_GRUPO_SERV");
        FCoGrupoCad.setLookupDesc("GRUPO");
        FCoGrupoCad.setFlex(true);
        FCoGrupoCad.setReadOnly(true);
        FCoGrupoCad.setRequired(false);
        FCoGrupoCad.setPrompt("Selecione");
        FCoGrupoCad.setConstraintCheckWhen("cwImmediate");
        FCoGrupoCad.setConstraintCheckType("ctExpression");
        FCoGrupoCad.setConstraintFocusOnError(false);
        FCoGrupoCad.setConstraintEnableUI(true);
        FCoGrupoCad.setConstraintEnabled(false);
        FCoGrupoCad.setConstraintFormCheck(true);
        FCoGrupoCad.setClearOnDelKey(true);
        FCoGrupoCad.setUseClearButton(true);
        FCoGrupoCad.setHideClearButtonOnNullValue(true);
        FCoGrupoCad.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FCoGrupoCadClearClick(event);
            processarFlow("FrmServicos", "FCoGrupoCad", "OnClearClick");
        });
        FVBox57.addChildren(FCoGrupoCad);
        FCoGrupoCad.applyProperties();
        addValidatable(FCoGrupoCad);
    }

    public TFVBox FVBox58 = new TFVBox();

    private void init_FVBox58() {
        FVBox58.setName("FVBox58");
        FVBox58.setLeft(185);
        FVBox58.setTop(0);
        FVBox58.setWidth(185);
        FVBox58.setHeight(41);
        FVBox58.setBorderStyle("stNone");
        FVBox58.setPaddingTop(0);
        FVBox58.setPaddingLeft(0);
        FVBox58.setPaddingRight(0);
        FVBox58.setPaddingBottom(0);
        FVBox58.setMarginTop(0);
        FVBox58.setMarginLeft(0);
        FVBox58.setMarginRight(0);
        FVBox58.setMarginBottom(0);
        FVBox58.setSpacing(1);
        FVBox58.setFlexVflex("ftMin");
        FVBox58.setFlexHflex("ftTrue");
        FVBox58.setScrollable(false);
        FVBox58.setBoxShadowConfigHorizontalLength(10);
        FVBox58.setBoxShadowConfigVerticalLength(10);
        FVBox58.setBoxShadowConfigBlurRadius(5);
        FVBox58.setBoxShadowConfigSpreadRadius(0);
        FVBox58.setBoxShadowConfigShadowColor("clBlack");
        FVBox58.setBoxShadowConfigOpacity(75);
        FHBox11.addChildren(FVBox58);
        FVBox58.applyProperties();
    }

    public TFLabel FLabel27 = new TFLabel();

    private void init_FLabel27() {
        FLabel27.setName("FLabel27");
        FLabel27.setLeft(0);
        FLabel27.setTop(0);
        FLabel27.setWidth(61);
        FLabel27.setHeight(13);
        FLabel27.setCaption("Classifica\u00E7\u00E3o");
        FLabel27.setFontColor("clWindowText");
        FLabel27.setFontSize(-11);
        FLabel27.setFontName("Tahoma");
        FLabel27.setFontStyle("[]");
        FLabel27.setVerticalAlignment("taVerticalCenter");
        FLabel27.setWordBreak(false);
        FVBox58.addChildren(FLabel27);
        FLabel27.applyProperties();
    }

    public TFCombo FCoClassCad = new TFCombo();

    private void init_FCoClassCad() {
        FCoClassCad.setName("FCoClassCad");
        FCoClassCad.setLeft(0);
        FCoClassCad.setTop(14);
        FCoClassCad.setWidth(145);
        FCoClassCad.setHeight(21);
        FCoClassCad.setTable(tbServicos);
        FCoClassCad.setLookupTable(tbClassificacaoServCadastro);
        FCoClassCad.setFieldName("COD_CLASSIFICACAO");
        FCoClassCad.setLookupKey("COD_CLASSIFICACAO");
        FCoClassCad.setLookupDesc("MONTADORA_CLASSIFICACAO");
        FCoClassCad.setFlex(true);
        FCoClassCad.setReadOnly(true);
        FCoClassCad.setRequired(false);
        FCoClassCad.setPrompt("Selecione");
        FCoClassCad.setConstraintCheckWhen("cwImmediate");
        FCoClassCad.setConstraintCheckType("ctExpression");
        FCoClassCad.setConstraintFocusOnError(false);
        FCoClassCad.setConstraintEnableUI(true);
        FCoClassCad.setConstraintEnabled(false);
        FCoClassCad.setConstraintFormCheck(true);
        FCoClassCad.setClearOnDelKey(true);
        FCoClassCad.setUseClearButton(true);
        FCoClassCad.setHideClearButtonOnNullValue(true);
        FVBox58.addChildren(FCoClassCad);
        FCoClassCad.applyProperties();
        addValidatable(FCoClassCad);
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(249);
        FHBox12.setWidth(540);
        FHBox12.setHeight(41);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftMin");
        FHBox12.setFlexHflex("ftTrue");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        vBoxTabCadastro.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFVBox FVBox59 = new TFVBox();

    private void init_FVBox59() {
        FVBox59.setName("FVBox59");
        FVBox59.setLeft(0);
        FVBox59.setTop(0);
        FVBox59.setWidth(185);
        FVBox59.setHeight(41);
        FVBox59.setBorderStyle("stNone");
        FVBox59.setPaddingTop(0);
        FVBox59.setPaddingLeft(0);
        FVBox59.setPaddingRight(0);
        FVBox59.setPaddingBottom(0);
        FVBox59.setMarginTop(0);
        FVBox59.setMarginLeft(0);
        FVBox59.setMarginRight(0);
        FVBox59.setMarginBottom(0);
        FVBox59.setSpacing(1);
        FVBox59.setFlexVflex("ftMin");
        FVBox59.setFlexHflex("ftTrue");
        FVBox59.setScrollable(false);
        FVBox59.setBoxShadowConfigHorizontalLength(10);
        FVBox59.setBoxShadowConfigVerticalLength(10);
        FVBox59.setBoxShadowConfigBlurRadius(5);
        FVBox59.setBoxShadowConfigSpreadRadius(0);
        FVBox59.setBoxShadowConfigShadowColor("clBlack");
        FVBox59.setBoxShadowConfigOpacity(75);
        FHBox12.addChildren(FVBox59);
        FVBox59.applyProperties();
    }

    public TFLabel FLabel35 = new TFLabel();

    private void init_FLabel35() {
        FLabel35.setName("FLabel35");
        FLabel35.setLeft(0);
        FLabel35.setTop(0);
        FLabel35.setWidth(47);
        FLabel35.setHeight(13);
        FLabel35.setCaption("SubGrupo");
        FLabel35.setFontColor("clWindowText");
        FLabel35.setFontSize(-11);
        FLabel35.setFontName("Tahoma");
        FLabel35.setFontStyle("[]");
        FLabel35.setVerticalAlignment("taVerticalCenter");
        FLabel35.setWordBreak(false);
        FVBox59.addChildren(FLabel35);
        FLabel35.applyProperties();
    }

    public TFCombo FCoSubGrupoCad = new TFCombo();

    private void init_FCoSubGrupoCad() {
        FCoSubGrupoCad.setName("FCoSubGrupoCad");
        FCoSubGrupoCad.setLeft(0);
        FCoSubGrupoCad.setTop(14);
        FCoSubGrupoCad.setWidth(145);
        FCoSubGrupoCad.setHeight(21);
        FCoSubGrupoCad.setTable(tbServicos);
        FCoSubGrupoCad.setLookupTable(tbServicosSubGrupoCadastro);
        FCoSubGrupoCad.setFieldName("COD_SUB_GRUPO_SERV");
        FCoSubGrupoCad.setLookupKey("COD_SUB_GRUPO_SERV");
        FCoSubGrupoCad.setLookupDesc("SUBGRUPO");
        FCoSubGrupoCad.setFlex(true);
        FCoSubGrupoCad.setReadOnly(true);
        FCoSubGrupoCad.setRequired(false);
        FCoSubGrupoCad.setPrompt("Selecione");
        FCoSubGrupoCad.setConstraintCheckWhen("cwImmediate");
        FCoSubGrupoCad.setConstraintCheckType("ctExpression");
        FCoSubGrupoCad.setConstraintFocusOnError(false);
        FCoSubGrupoCad.setConstraintEnableUI(true);
        FCoSubGrupoCad.setConstraintEnabled(false);
        FCoSubGrupoCad.setConstraintFormCheck(true);
        FCoSubGrupoCad.setClearOnDelKey(true);
        FCoSubGrupoCad.setUseClearButton(false);
        FCoSubGrupoCad.setHideClearButtonOnNullValue(true);
        FVBox59.addChildren(FCoSubGrupoCad);
        FCoSubGrupoCad.applyProperties();
        addValidatable(FCoSubGrupoCad);
    }

    public TFVBox FVBox60 = new TFVBox();

    private void init_FVBox60() {
        FVBox60.setName("FVBox60");
        FVBox60.setLeft(185);
        FVBox60.setTop(0);
        FVBox60.setWidth(185);
        FVBox60.setHeight(41);
        FVBox60.setBorderStyle("stNone");
        FVBox60.setPaddingTop(0);
        FVBox60.setPaddingLeft(0);
        FVBox60.setPaddingRight(0);
        FVBox60.setPaddingBottom(0);
        FVBox60.setMarginTop(0);
        FVBox60.setMarginLeft(0);
        FVBox60.setMarginRight(0);
        FVBox60.setMarginBottom(0);
        FVBox60.setSpacing(1);
        FVBox60.setFlexVflex("ftMin");
        FVBox60.setFlexHflex("ftTrue");
        FVBox60.setScrollable(false);
        FVBox60.setBoxShadowConfigHorizontalLength(10);
        FVBox60.setBoxShadowConfigVerticalLength(10);
        FVBox60.setBoxShadowConfigBlurRadius(5);
        FVBox60.setBoxShadowConfigSpreadRadius(0);
        FVBox60.setBoxShadowConfigShadowColor("clBlack");
        FVBox60.setBoxShadowConfigOpacity(75);
        FHBox12.addChildren(FVBox60);
        FVBox60.applyProperties();
    }

    public TFLabel FLabel37 = new TFLabel();

    private void init_FLabel37() {
        FLabel37.setName("FLabel37");
        FLabel37.setLeft(0);
        FLabel37.setTop(0);
        FLabel37.setWidth(26);
        FLabel37.setHeight(13);
        FLabel37.setCaption("Setor");
        FLabel37.setFontColor("clWindowText");
        FLabel37.setFontSize(-11);
        FLabel37.setFontName("Tahoma");
        FLabel37.setFontStyle("[]");
        FLabel37.setVerticalAlignment("taVerticalCenter");
        FLabel37.setWordBreak(false);
        FVBox60.addChildren(FLabel37);
        FLabel37.applyProperties();
    }

    public TFCombo FCoSetorCad = new TFCombo();

    private void init_FCoSetorCad() {
        FCoSetorCad.setName("FCoSetorCad");
        FCoSetorCad.setLeft(0);
        FCoSetorCad.setTop(14);
        FCoSetorCad.setWidth(145);
        FCoSetorCad.setHeight(21);
        FCoSetorCad.setTable(tbServicos);
        FCoSetorCad.setLookupTable(tbServicosSetoresCadastro);
        FCoSetorCad.setFieldName("COD_SETOR");
        FCoSetorCad.setLookupKey("COD_SETOR");
        FCoSetorCad.setLookupDesc("DESCRICAO_SETOR");
        FCoSetorCad.setFlex(true);
        FCoSetorCad.setReadOnly(true);
        FCoSetorCad.setRequired(true);
        FCoSetorCad.setPrompt("Selecione");
        FCoSetorCad.setConstraintCheckWhen("cwImmediate");
        FCoSetorCad.setConstraintCheckType("ctExpression");
        FCoSetorCad.setConstraintFocusOnError(false);
        FCoSetorCad.setConstraintEnableUI(true);
        FCoSetorCad.setConstraintEnabled(false);
        FCoSetorCad.setConstraintFormCheck(true);
        FCoSetorCad.setClearOnDelKey(true);
        FCoSetorCad.setUseClearButton(false);
        FCoSetorCad.setHideClearButtonOnNullValue(true);
        FVBox60.addChildren(FCoSetorCad);
        FCoSetorCad.applyProperties();
        addValidatable(FCoSetorCad);
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(0);
        FHBox13.setTop(291);
        FHBox13.setWidth(540);
        FHBox13.setHeight(53);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftMin");
        FHBox13.setFlexHflex("ftTrue");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        vBoxTabCadastro.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(0);
        FVBox8.setTop(0);
        FVBox8.setWidth(185);
        FVBox8.setHeight(53);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftTrue");
        FVBox8.setFlexHflex("ftTrue");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        FHBox13.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(0);
        FLabel4.setTop(0);
        FLabel4.setWidth(85);
        FLabel4.setHeight(13);
        FLabel4.setCaption("C\u00F3digo LC 116/03");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-11);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        FVBox8.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFCombo FCbCodigoLC = new TFCombo();

    private void init_FCbCodigoLC() {
        FCbCodigoLC.setName("FCbCodigoLC");
        FCbCodigoLC.setLeft(0);
        FCbCodigoLC.setTop(14);
        FCbCodigoLC.setWidth(166);
        FCbCodigoLC.setHeight(21);
        FCbCodigoLC.setTable(tbServicos);
        FCbCodigoLC.setLookupTable(tbServicosLc1162003);
        FCbCodigoLC.setFieldName("COD_SERV_LC11603");
        FCbCodigoLC.setLookupKey("CODIGO");
        FCbCodigoLC.setLookupDesc("DESC2");
        FCbCodigoLC.setFlex(true);
        FCbCodigoLC.setReadOnly(true);
        FCbCodigoLC.setRequired(false);
        FCbCodigoLC.setPrompt("Selecione");
        FCbCodigoLC.setConstraintCheckWhen("cwImmediate");
        FCbCodigoLC.setConstraintCheckType("ctExpression");
        FCbCodigoLC.setConstraintFocusOnError(false);
        FCbCodigoLC.setConstraintEnableUI(true);
        FCbCodigoLC.setConstraintEnabled(false);
        FCbCodigoLC.setConstraintFormCheck(true);
        FCbCodigoLC.setClearOnDelKey(true);
        FCbCodigoLC.setUseClearButton(false);
        FCbCodigoLC.setHideClearButtonOnNullValue(true);
        FVBox8.addChildren(FCbCodigoLC);
        FCbCodigoLC.applyProperties();
        addValidatable(FCbCodigoLC);
    }

    public TFVBox FVBox10 = new TFVBox();

    private void init_FVBox10() {
        FVBox10.setName("FVBox10");
        FVBox10.setLeft(185);
        FVBox10.setTop(0);
        FVBox10.setWidth(353);
        FVBox10.setHeight(53);
        FVBox10.setBorderStyle("stNone");
        FVBox10.setPaddingTop(0);
        FVBox10.setPaddingLeft(5);
        FVBox10.setPaddingRight(0);
        FVBox10.setPaddingBottom(0);
        FVBox10.setMarginTop(0);
        FVBox10.setMarginLeft(0);
        FVBox10.setMarginRight(0);
        FVBox10.setMarginBottom(0);
        FVBox10.setSpacing(1);
        FVBox10.setFlexVflex("ftTrue");
        FVBox10.setFlexHflex("ftTrue");
        FVBox10.setScrollable(false);
        FVBox10.setBoxShadowConfigHorizontalLength(10);
        FVBox10.setBoxShadowConfigVerticalLength(10);
        FVBox10.setBoxShadowConfigBlurRadius(5);
        FVBox10.setBoxShadowConfigSpreadRadius(0);
        FVBox10.setBoxShadowConfigShadowColor("clBlack");
        FVBox10.setBoxShadowConfigOpacity(75);
        FHBox13.addChildren(FVBox10);
        FVBox10.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(0);
        FLabel5.setWidth(55);
        FLabel5.setHeight(13);
        FLabel5.setCaption("C\u00F3digo NBS");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        FVBox10.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFHBox FHBox26 = new TFHBox();

    private void init_FHBox26() {
        FHBox26.setName("FHBox26");
        FHBox26.setLeft(0);
        FHBox26.setTop(14);
        FHBox26.setWidth(351);
        FHBox26.setHeight(33);
        FHBox26.setBorderStyle("stNone");
        FHBox26.setPaddingTop(0);
        FHBox26.setPaddingLeft(0);
        FHBox26.setPaddingRight(0);
        FHBox26.setPaddingBottom(0);
        FHBox26.setMarginTop(0);
        FHBox26.setMarginLeft(0);
        FHBox26.setMarginRight(0);
        FHBox26.setMarginBottom(0);
        FHBox26.setSpacing(5);
        FHBox26.setFlexVflex("ftTrue");
        FHBox26.setFlexHflex("ftTrue");
        FHBox26.setScrollable(false);
        FHBox26.setBoxShadowConfigHorizontalLength(10);
        FHBox26.setBoxShadowConfigVerticalLength(10);
        FHBox26.setBoxShadowConfigBlurRadius(5);
        FHBox26.setBoxShadowConfigSpreadRadius(0);
        FHBox26.setBoxShadowConfigShadowColor("clBlack");
        FHBox26.setBoxShadowConfigOpacity(75);
        FHBox26.setVAlign("tvTop");
        FVBox10.addChildren(FHBox26);
        FHBox26.applyProperties();
    }

    public TFString FStrCodNbsDesc = new TFString();

    private void init_FStrCodNbsDesc() {
        FStrCodNbsDesc.setName("FStrCodNbsDesc");
        FStrCodNbsDesc.setLeft(0);
        FStrCodNbsDesc.setTop(0);
        FStrCodNbsDesc.setWidth(160);
        FStrCodNbsDesc.setHeight(24);
        FStrCodNbsDesc.setFlex(true);
        FStrCodNbsDesc.setRequired(false);
        FStrCodNbsDesc.setConstraintCheckWhen("cwImmediate");
        FStrCodNbsDesc.setConstraintCheckType("ctExpression");
        FStrCodNbsDesc.setConstraintFocusOnError(false);
        FStrCodNbsDesc.setConstraintEnableUI(true);
        FStrCodNbsDesc.setConstraintEnabled(false);
        FStrCodNbsDesc.setConstraintFormCheck(true);
        FStrCodNbsDesc.setCharCase("ccNormal");
        FStrCodNbsDesc.setPwd(false);
        FStrCodNbsDesc.setMaxlength(0);
        FStrCodNbsDesc.setEnabled(false);
        FStrCodNbsDesc.setFontColor("clWindowText");
        FStrCodNbsDesc.setFontSize(-13);
        FStrCodNbsDesc.setFontName("Tahoma");
        FStrCodNbsDesc.setFontStyle("[]");
        FStrCodNbsDesc.setSaveLiteralCharacter(false);
        FStrCodNbsDesc.applyProperties();
        FHBox26.addChildren(FStrCodNbsDesc);
        addValidatable(FStrCodNbsDesc);
    }

    public TFButton btnPesqCodNBS = new TFButton();

    private void init_btnPesqCodNBS() {
        btnPesqCodNBS.setName("btnPesqCodNBS");
        btnPesqCodNBS.setLeft(160);
        btnPesqCodNBS.setTop(0);
        btnPesqCodNBS.setWidth(37);
        btnPesqCodNBS.setHeight(36);
        btnPesqCodNBS.setFontColor("clWindowText");
        btnPesqCodNBS.setFontSize(-11);
        btnPesqCodNBS.setFontName("Tahoma");
        btnPesqCodNBS.setFontStyle("[]");
        btnPesqCodNBS.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesqCodNBSClick(event);
            processarFlow("FrmServicos", "btnPesqCodNBS", "OnClick");
        });
        btnPesqCodNBS.setImageId(430070);
        btnPesqCodNBS.setColor("clBtnFace");
        btnPesqCodNBS.setAccess(false);
        btnPesqCodNBS.setIconReverseDirection(false);
        FHBox26.addChildren(btnPesqCodNBS);
        btnPesqCodNBS.applyProperties();
    }

    public TFString FStrCodNBS = new TFString();

    private void init_FStrCodNBS() {
        FStrCodNBS.setName("FStrCodNBS");
        FStrCodNBS.setLeft(197);
        FStrCodNBS.setTop(0);
        FStrCodNBS.setWidth(25);
        FStrCodNBS.setHeight(24);
        FStrCodNBS.setTable(tbServicos);
        FStrCodNBS.setFieldName("COD_NBS");
        FStrCodNBS.setFlex(false);
        FStrCodNBS.setRequired(false);
        FStrCodNBS.setConstraintCheckWhen("cwImmediate");
        FStrCodNBS.setConstraintCheckType("ctExpression");
        FStrCodNBS.setConstraintFocusOnError(false);
        FStrCodNBS.setConstraintEnableUI(true);
        FStrCodNBS.setConstraintEnabled(false);
        FStrCodNBS.setConstraintFormCheck(true);
        FStrCodNBS.setCharCase("ccNormal");
        FStrCodNBS.setPwd(false);
        FStrCodNBS.setMaxlength(0);
        FStrCodNBS.setVisible(false);
        FStrCodNBS.setFontColor("clWindowText");
        FStrCodNBS.setFontSize(-13);
        FStrCodNBS.setFontName("Tahoma");
        FStrCodNBS.setFontStyle("[]");
        FStrCodNBS.setSaveLiteralCharacter(false);
        FStrCodNBS.applyProperties();
        FHBox26.addChildren(FStrCodNBS);
        addValidatable(FStrCodNBS);
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(0);
        FHBox14.setTop(345);
        FHBox14.setWidth(540);
        FHBox14.setHeight(57);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(5);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(5);
        FHBox14.setFlexVflex("ftMin");
        FHBox14.setFlexHflex("ftTrue");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        vBoxTabCadastro.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFVBox FVBox11 = new TFVBox();

    private void init_FVBox11() {
        FVBox11.setName("FVBox11");
        FVBox11.setLeft(0);
        FVBox11.setTop(0);
        FVBox11.setWidth(538);
        FVBox11.setHeight(56);
        FVBox11.setBorderStyle("stNone");
        FVBox11.setPaddingTop(0);
        FVBox11.setPaddingLeft(0);
        FVBox11.setPaddingRight(0);
        FVBox11.setPaddingBottom(0);
        FVBox11.setMarginTop(0);
        FVBox11.setMarginLeft(0);
        FVBox11.setMarginRight(0);
        FVBox11.setMarginBottom(0);
        FVBox11.setSpacing(15);
        FVBox11.setFlexVflex("ftTrue");
        FVBox11.setFlexHflex("ftTrue");
        FVBox11.setScrollable(false);
        FVBox11.setBoxShadowConfigHorizontalLength(10);
        FVBox11.setBoxShadowConfigVerticalLength(10);
        FVBox11.setBoxShadowConfigBlurRadius(5);
        FVBox11.setBoxShadowConfigSpreadRadius(0);
        FVBox11.setBoxShadowConfigShadowColor("clBlack");
        FVBox11.setBoxShadowConfigOpacity(75);
        FHBox14.addChildren(FVBox11);
        FVBox11.applyProperties();
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(0);
        FHBox16.setTop(0);
        FHBox16.setWidth(536);
        FHBox16.setHeight(50);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(5);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(5);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(5);
        FHBox16.setFlexVflex("ftTrue");
        FHBox16.setFlexHflex("ftTrue");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvMiddle");
        FVBox11.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFVBox FVBox16 = new TFVBox();

    private void init_FVBox16() {
        FVBox16.setName("FVBox16");
        FVBox16.setLeft(0);
        FVBox16.setTop(0);
        FVBox16.setWidth(79);
        FVBox16.setHeight(47);
        FVBox16.setBorderStyle("stNone");
        FVBox16.setPaddingTop(8);
        FVBox16.setPaddingLeft(0);
        FVBox16.setPaddingRight(0);
        FVBox16.setPaddingBottom(0);
        FVBox16.setMarginTop(0);
        FVBox16.setMarginLeft(0);
        FVBox16.setMarginRight(0);
        FVBox16.setMarginBottom(0);
        FVBox16.setSpacing(1);
        FVBox16.setFlexVflex("ftFalse");
        FVBox16.setFlexHflex("ftFalse");
        FVBox16.setScrollable(false);
        FVBox16.setBoxShadowConfigHorizontalLength(10);
        FVBox16.setBoxShadowConfigVerticalLength(10);
        FVBox16.setBoxShadowConfigBlurRadius(5);
        FVBox16.setBoxShadowConfigSpreadRadius(0);
        FVBox16.setBoxShadowConfigShadowColor("clBlack");
        FVBox16.setBoxShadowConfigOpacity(75);
        FHBox16.addChildren(FVBox16);
        FVBox16.applyProperties();
    }

    public TFCheckBox ckbTerCad = new TFCheckBox();

    private void init_ckbTerCad() {
        ckbTerCad.setName("ckbTerCad");
        ckbTerCad.setLeft(0);
        ckbTerCad.setTop(0);
        ckbTerCad.setWidth(86);
        ckbTerCad.setHeight(17);
        ckbTerCad.setCaption("Terceiros");
        ckbTerCad.setFontColor("clWindowText");
        ckbTerCad.setFontSize(-11);
        ckbTerCad.setFontName("Tahoma");
        ckbTerCad.setFontStyle("[]");
        ckbTerCad.setTable(tbServicos);
        ckbTerCad.setFieldName("TERCEIROS");
        ckbTerCad.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            ckbTerCadCheck(event);
            processarFlow("FrmServicos", "ckbTerCad", "OnCheck");
        });
        ckbTerCad.setVerticalAlignment("taAlignTop");
        FVBox16.addChildren(ckbTerCad);
        ckbTerCad.applyProperties();
    }

    public TFString strPesqCad = new TFString();

    private void init_strPesqCad() {
        strPesqCad.setName("strPesqCad");
        strPesqCad.setLeft(79);
        strPesqCad.setTop(0);
        strPesqCad.setWidth(305);
        strPesqCad.setHeight(24);
        strPesqCad.setFlex(true);
        strPesqCad.setRequired(false);
        strPesqCad.setPrompt("escolha o 'Terceiro' no bot\u00E3o pesquisar");
        strPesqCad.setConstraintCheckWhen("cwImmediate");
        strPesqCad.setConstraintCheckType("ctExpression");
        strPesqCad.setConstraintFocusOnError(false);
        strPesqCad.setConstraintEnableUI(true);
        strPesqCad.setConstraintEnabled(false);
        strPesqCad.setConstraintFormCheck(true);
        strPesqCad.setCharCase("ccNormal");
        strPesqCad.setPwd(false);
        strPesqCad.setMaxlength(0);
        strPesqCad.setFontColor("clWindowText");
        strPesqCad.setFontSize(-13);
        strPesqCad.setFontName("Tahoma");
        strPesqCad.setFontStyle("[]");
        strPesqCad.setSaveLiteralCharacter(false);
        strPesqCad.applyProperties();
        FHBox16.addChildren(strPesqCad);
        addValidatable(strPesqCad);
    }

    public TFButton FBtnPesquisar = new TFButton();

    private void init_FBtnPesquisar() {
        FBtnPesquisar.setName("FBtnPesquisar");
        FBtnPesquisar.setLeft(384);
        FBtnPesquisar.setTop(0);
        FBtnPesquisar.setWidth(38);
        FBtnPesquisar.setHeight(37);
        FBtnPesquisar.setFontColor("clWindowText");
        FBtnPesquisar.setFontSize(-11);
        FBtnPesquisar.setFontName("Tahoma");
        FBtnPesquisar.setFontStyle("[]");
        FBtnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FBtnPesquisarClick(event);
            processarFlow("FrmServicos", "FBtnPesquisar", "OnClick");
        });
        FBtnPesquisar.setImageId(430070);
        FBtnPesquisar.setColor("clBtnFace");
        FBtnPesquisar.setAccess(false);
        FBtnPesquisar.setIconReverseDirection(false);
        FHBox16.addChildren(FBtnPesquisar);
        FBtnPesquisar.applyProperties();
    }

    public TFInteger FIntCodCliente = new TFInteger();

    private void init_FIntCodCliente() {
        FIntCodCliente.setName("FIntCodCliente");
        FIntCodCliente.setLeft(422);
        FIntCodCliente.setTop(0);
        FIntCodCliente.setWidth(113);
        FIntCodCliente.setHeight(24);
        FIntCodCliente.setTable(tbServicos);
        FIntCodCliente.setFieldName("COD_CLIENTE");
        FIntCodCliente.setFlex(false);
        FIntCodCliente.setRequired(false);
        FIntCodCliente.setConstraintCheckWhen("cwImmediate");
        FIntCodCliente.setConstraintCheckType("ctExpression");
        FIntCodCliente.setConstraintFocusOnError(false);
        FIntCodCliente.setConstraintEnableUI(true);
        FIntCodCliente.setConstraintEnabled(false);
        FIntCodCliente.setConstraintFormCheck(true);
        FIntCodCliente.setMaxlength(0);
        FIntCodCliente.setVisible(false);
        FIntCodCliente.setFontColor("clWindowText");
        FIntCodCliente.setFontSize(-13);
        FIntCodCliente.setFontName("Tahoma");
        FIntCodCliente.setFontStyle("[]");
        FIntCodCliente.setAlignment("taRightJustify");
        FHBox16.addChildren(FIntCodCliente);
        FIntCodCliente.applyProperties();
        addValidatable(FIntCodCliente);
    }

    public TFVBox FVBox12 = new TFVBox();

    private void init_FVBox12() {
        FVBox12.setName("FVBox12");
        FVBox12.setLeft(0);
        FVBox12.setTop(403);
        FVBox12.setWidth(540);
        FVBox12.setHeight(120);
        FVBox12.setBorderStyle("stNone");
        FVBox12.setPaddingTop(0);
        FVBox12.setPaddingLeft(0);
        FVBox12.setPaddingRight(0);
        FVBox12.setPaddingBottom(0);
        FVBox12.setMarginTop(0);
        FVBox12.setMarginLeft(0);
        FVBox12.setMarginRight(0);
        FVBox12.setMarginBottom(0);
        FVBox12.setSpacing(15);
        FVBox12.setFlexVflex("ftFalse");
        FVBox12.setFlexHflex("ftTrue");
        FVBox12.setScrollable(false);
        FVBox12.setBoxShadowConfigHorizontalLength(10);
        FVBox12.setBoxShadowConfigVerticalLength(10);
        FVBox12.setBoxShadowConfigBlurRadius(5);
        FVBox12.setBoxShadowConfigSpreadRadius(0);
        FVBox12.setBoxShadowConfigShadowColor("clBlack");
        FVBox12.setBoxShadowConfigOpacity(75);
        vBoxTabCadastro.addChildren(FVBox12);
        FVBox12.applyProperties();
    }

    public TFHBox FHBox20 = new TFHBox();

    private void init_FHBox20() {
        FHBox20.setName("FHBox20");
        FHBox20.setLeft(0);
        FHBox20.setTop(0);
        FHBox20.setWidth(539);
        FHBox20.setHeight(108);
        FHBox20.setBorderStyle("stNone");
        FHBox20.setPaddingTop(0);
        FHBox20.setPaddingLeft(0);
        FHBox20.setPaddingRight(0);
        FHBox20.setPaddingBottom(0);
        FHBox20.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FHBox20Click(event);
            processarFlow("FrmServicos", "FHBox20", "OnClick");
        });
        FHBox20.setMarginTop(0);
        FHBox20.setMarginLeft(0);
        FHBox20.setMarginRight(0);
        FHBox20.setMarginBottom(0);
        FHBox20.setSpacing(1);
        FHBox20.setFlexVflex("ftFalse");
        FHBox20.setFlexHflex("ftTrue");
        FHBox20.setScrollable(true);
        FHBox20.setBoxShadowConfigHorizontalLength(10);
        FHBox20.setBoxShadowConfigVerticalLength(10);
        FHBox20.setBoxShadowConfigBlurRadius(5);
        FHBox20.setBoxShadowConfigSpreadRadius(0);
        FHBox20.setBoxShadowConfigShadowColor("clBlack");
        FHBox20.setBoxShadowConfigOpacity(75);
        FHBox20.setVAlign("tvTop");
        FVBox12.addChildren(FHBox20);
        FHBox20.applyProperties();
    }

    public TFVBox FVBox13 = new TFVBox();

    private void init_FVBox13() {
        FVBox13.setName("FVBox13");
        FVBox13.setLeft(0);
        FVBox13.setTop(0);
        FVBox13.setWidth(161);
        FVBox13.setHeight(97);
        FVBox13.setBorderStyle("stNone");
        FVBox13.setPaddingTop(0);
        FVBox13.setPaddingLeft(0);
        FVBox13.setPaddingRight(0);
        FVBox13.setPaddingBottom(0);
        FVBox13.setMarginTop(0);
        FVBox13.setMarginLeft(0);
        FVBox13.setMarginRight(0);
        FVBox13.setMarginBottom(0);
        FVBox13.setSpacing(2);
        FVBox13.setFlexVflex("ftFalse");
        FVBox13.setFlexHflex("ftTrue");
        FVBox13.setScrollable(false);
        FVBox13.setBoxShadowConfigHorizontalLength(10);
        FVBox13.setBoxShadowConfigVerticalLength(10);
        FVBox13.setBoxShadowConfigBlurRadius(5);
        FVBox13.setBoxShadowConfigSpreadRadius(0);
        FVBox13.setBoxShadowConfigShadowColor("clBlack");
        FVBox13.setBoxShadowConfigOpacity(75);
        FHBox20.addChildren(FVBox13);
        FVBox13.applyProperties();
    }

    public TFCheckBox ckbLubCad = new TFCheckBox();

    private void init_ckbLubCad() {
        ckbLubCad.setName("ckbLubCad");
        ckbLubCad.setLeft(0);
        ckbLubCad.setTop(0);
        ckbLubCad.setWidth(82);
        ckbLubCad.setHeight(17);
        ckbLubCad.setCaption("Lubrifica\u00E7\u00E3o");
        ckbLubCad.setFontColor("clWindowText");
        ckbLubCad.setFontSize(-11);
        ckbLubCad.setFontName("Tahoma");
        ckbLubCad.setFontStyle("[]");
        ckbLubCad.setTable(tbServicos);
        ckbLubCad.setFieldName("LUBRIFICACAO");
        ckbLubCad.setVerticalAlignment("taAlignTop");
        FVBox13.addChildren(ckbLubCad);
        ckbLubCad.applyProperties();
    }

    public TFCheckBox ckbTrocaOleoCad = new TFCheckBox();

    private void init_ckbTrocaOleoCad() {
        ckbTrocaOleoCad.setName("ckbTrocaOleoCad");
        ckbTrocaOleoCad.setLeft(0);
        ckbTrocaOleoCad.setTop(18);
        ckbTrocaOleoCad.setWidth(89);
        ckbTrocaOleoCad.setHeight(17);
        ckbTrocaOleoCad.setCaption("Troca de \u00F2leo");
        ckbTrocaOleoCad.setFontColor("clWindowText");
        ckbTrocaOleoCad.setFontSize(-11);
        ckbTrocaOleoCad.setFontName("Tahoma");
        ckbTrocaOleoCad.setFontStyle("[]");
        ckbTrocaOleoCad.setTable(tbServicos);
        ckbTrocaOleoCad.setFieldName("TROCA_OLEO");
        ckbTrocaOleoCad.setVerticalAlignment("taAlignTop");
        FVBox13.addChildren(ckbTrocaOleoCad);
        ckbTrocaOleoCad.applyProperties();
    }

    public TFCheckBox ckbLavagemCad = new TFCheckBox();

    private void init_ckbLavagemCad() {
        ckbLavagemCad.setName("ckbLavagemCad");
        ckbLavagemCad.setLeft(0);
        ckbLavagemCad.setTop(36);
        ckbLavagemCad.setWidth(69);
        ckbLavagemCad.setHeight(17);
        ckbLavagemCad.setCaption("Lavagem");
        ckbLavagemCad.setFontColor("clWindowText");
        ckbLavagemCad.setFontSize(-11);
        ckbLavagemCad.setFontName("Tahoma");
        ckbLavagemCad.setFontStyle("[]");
        ckbLavagemCad.setTable(tbServicos);
        ckbLavagemCad.setFieldName("LAVAGEM");
        ckbLavagemCad.setVerticalAlignment("taAlignTop");
        FVBox13.addChildren(ckbLavagemCad);
        ckbLavagemCad.applyProperties();
    }

    public TFVBox FVBox15 = new TFVBox();

    private void init_FVBox15() {
        FVBox15.setName("FVBox15");
        FVBox15.setLeft(161);
        FVBox15.setTop(0);
        FVBox15.setWidth(185);
        FVBox15.setHeight(96);
        FVBox15.setBorderStyle("stNone");
        FVBox15.setPaddingTop(0);
        FVBox15.setPaddingLeft(0);
        FVBox15.setPaddingRight(0);
        FVBox15.setPaddingBottom(0);
        FVBox15.setMarginTop(0);
        FVBox15.setMarginLeft(0);
        FVBox15.setMarginRight(0);
        FVBox15.setMarginBottom(0);
        FVBox15.setSpacing(2);
        FVBox15.setFlexVflex("ftFalse");
        FVBox15.setFlexHflex("ftTrue");
        FVBox15.setScrollable(false);
        FVBox15.setBoxShadowConfigHorizontalLength(10);
        FVBox15.setBoxShadowConfigVerticalLength(10);
        FVBox15.setBoxShadowConfigBlurRadius(5);
        FVBox15.setBoxShadowConfigSpreadRadius(0);
        FVBox15.setBoxShadowConfigShadowColor("clBlack");
        FVBox15.setBoxShadowConfigOpacity(75);
        FHBox20.addChildren(FVBox15);
        FVBox15.applyProperties();
    }

    public TFCheckBox ckbPassiRemeCad = new TFCheckBox();

    private void init_ckbPassiRemeCad() {
        ckbPassiRemeCad.setName("ckbPassiRemeCad");
        ckbPassiRemeCad.setLeft(0);
        ckbPassiRemeCad.setTop(0);
        ckbPassiRemeCad.setWidth(126);
        ckbPassiRemeCad.setHeight(17);
        ckbPassiRemeCad.setCaption("Pass\u00EDvel de Remessa");
        ckbPassiRemeCad.setFontColor("clWindowText");
        ckbPassiRemeCad.setFontSize(-11);
        ckbPassiRemeCad.setFontName("Tahoma");
        ckbPassiRemeCad.setFontStyle("[]");
        ckbPassiRemeCad.setTable(tbServicos);
        ckbPassiRemeCad.setFieldName("PODE_TER_REMESSA");
        ckbPassiRemeCad.setVerticalAlignment("taAlignTop");
        FVBox15.addChildren(ckbPassiRemeCad);
        ckbPassiRemeCad.applyProperties();
    }

    public TFCheckBox ckbNApontarCad = new TFCheckBox();

    private void init_ckbNApontarCad() {
        ckbNApontarCad.setName("ckbNApontarCad");
        ckbNApontarCad.setLeft(0);
        ckbNApontarCad.setTop(18);
        ckbNApontarCad.setWidth(152);
        ckbNApontarCad.setHeight(17);
        ckbNApontarCad.setCaption("N\u00E3o Permitir Apontamento");
        ckbNApontarCad.setFontColor("clWindowText");
        ckbNApontarCad.setFontSize(-11);
        ckbNApontarCad.setFontName("Tahoma");
        ckbNApontarCad.setFontStyle("[]");
        ckbNApontarCad.setTable(tbServicos);
        ckbNApontarCad.setFieldName("NAO_PERMITE_APONTAMENTO");
        ckbNApontarCad.setVerticalAlignment("taAlignTop");
        FVBox15.addChildren(ckbNApontarCad);
        ckbNApontarCad.applyProperties();
    }

    public TFCheckBox ckbTerRemeCad = new TFCheckBox();

    private void init_ckbTerRemeCad() {
        ckbTerRemeCad.setName("ckbTerRemeCad");
        ckbTerRemeCad.setLeft(0);
        ckbTerRemeCad.setTop(36);
        ckbTerRemeCad.setWidth(125);
        ckbTerRemeCad.setHeight(17);
        ckbTerRemeCad.setCaption("Terceiros Remessa");
        ckbTerRemeCad.setFontColor("clWindowText");
        ckbTerRemeCad.setFontSize(-11);
        ckbTerRemeCad.setFontName("Tahoma");
        ckbTerRemeCad.setFontStyle("[]");
        ckbTerRemeCad.setTable(tbServicos);
        ckbTerRemeCad.setFieldName("TERCEIRO_REMESSA");
        ckbTerRemeCad.setVerticalAlignment("taAlignTop");
        FVBox15.addChildren(ckbTerRemeCad);
        ckbTerRemeCad.applyProperties();
    }

    public TFVBox FVBox64 = new TFVBox();

    private void init_FVBox64() {
        FVBox64.setName("FVBox64");
        FVBox64.setLeft(346);
        FVBox64.setTop(0);
        FVBox64.setWidth(185);
        FVBox64.setHeight(96);
        FVBox64.setBorderStyle("stNone");
        FVBox64.setPaddingTop(0);
        FVBox64.setPaddingLeft(0);
        FVBox64.setPaddingRight(0);
        FVBox64.setPaddingBottom(0);
        FVBox64.setMarginTop(0);
        FVBox64.setMarginLeft(0);
        FVBox64.setMarginRight(0);
        FVBox64.setMarginBottom(0);
        FVBox64.setSpacing(1);
        FVBox64.setFlexVflex("ftFalse");
        FVBox64.setFlexHflex("ftTrue");
        FVBox64.setScrollable(false);
        FVBox64.setBoxShadowConfigHorizontalLength(10);
        FVBox64.setBoxShadowConfigVerticalLength(10);
        FVBox64.setBoxShadowConfigBlurRadius(5);
        FVBox64.setBoxShadowConfigSpreadRadius(0);
        FVBox64.setBoxShadowConfigShadowColor("clBlack");
        FVBox64.setBoxShadowConfigOpacity(75);
        FHBox20.addChildren(FVBox64);
        FVBox64.applyProperties();
    }

    public TFCheckBox ckbNForcarCad = new TFCheckBox();

    private void init_ckbNForcarCad() {
        ckbNForcarCad.setName("ckbNForcarCad");
        ckbNForcarCad.setLeft(0);
        ckbNForcarCad.setTop(0);
        ckbNForcarCad.setWidth(147);
        ckbNForcarCad.setHeight(17);
        ckbNForcarCad.setCaption("N\u00E3o For\u00E7ar Apontamento");
        ckbNForcarCad.setFontColor("clWindowText");
        ckbNForcarCad.setFontSize(-11);
        ckbNForcarCad.setFontName("Tahoma");
        ckbNForcarCad.setFontStyle("[]");
        ckbNForcarCad.setTable(tbServicos);
        ckbNForcarCad.setFieldName("NAO_FORCAR_APONTAMENTO");
        ckbNForcarCad.setVerticalAlignment("taAlignTop");
        FVBox64.addChildren(ckbNForcarCad);
        ckbNForcarCad.applyProperties();
    }

    public TFCheckBox ckbAtivo = new TFCheckBox();

    private void init_ckbAtivo() {
        ckbAtivo.setName("ckbAtivo");
        ckbAtivo.setLeft(0);
        ckbAtivo.setTop(18);
        ckbAtivo.setWidth(97);
        ckbAtivo.setHeight(17);
        ckbAtivo.setCaption("Ativo");
        ckbAtivo.setColor("clBtnFace");
        ckbAtivo.setFontColor("clBlack");
        ckbAtivo.setFontSize(-11);
        ckbAtivo.setFontName("Tahoma");
        ckbAtivo.setFontStyle("[]");
        ckbAtivo.setTable(tbServicos);
        ckbAtivo.setFieldName("ATIVO");
        ckbAtivo.setCheckedValue("S");
        ckbAtivo.setUncheckedValue("N");
        ckbAtivo.setVerticalAlignment("taAlignTop");
        FVBox64.addChildren(ckbAtivo);
        ckbAtivo.applyProperties();
    }

    public TFTabsheet tabValor = new TFTabsheet();

    private void init_tabValor() {
        tabValor.setName("tabValor");
        tabValor.setCaption("Valor");
        tabValor.setVisible(true);
        tabValor.setClosable(false);
        pageControlServico.addChildren(tabValor);
        tabValor.applyProperties();
    }

    public TFVBox FVBox19 = new TFVBox();

    private void init_FVBox19() {
        FVBox19.setName("FVBox19");
        FVBox19.setLeft(0);
        FVBox19.setTop(0);
        FVBox19.setWidth(546);
        FVBox19.setHeight(772);
        FVBox19.setAlign("alClient");
        FVBox19.setBorderStyle("stNone");
        FVBox19.setPaddingTop(0);
        FVBox19.setPaddingLeft(5);
        FVBox19.setPaddingRight(5);
        FVBox19.setPaddingBottom(0);
        FVBox19.setMarginTop(0);
        FVBox19.setMarginLeft(0);
        FVBox19.setMarginRight(0);
        FVBox19.setMarginBottom(0);
        FVBox19.setSpacing(5);
        FVBox19.setFlexVflex("ftTrue");
        FVBox19.setFlexHflex("ftTrue");
        FVBox19.setScrollable(false);
        FVBox19.setBoxShadowConfigHorizontalLength(10);
        FVBox19.setBoxShadowConfigVerticalLength(10);
        FVBox19.setBoxShadowConfigBlurRadius(5);
        FVBox19.setBoxShadowConfigSpreadRadius(0);
        FVBox19.setBoxShadowConfigShadowColor("clBlack");
        FVBox19.setBoxShadowConfigOpacity(75);
        tabValor.addChildren(FVBox19);
        FVBox19.applyProperties();
    }

    public TFVBox FVBox20 = new TFVBox();

    private void init_FVBox20() {
        FVBox20.setName("FVBox20");
        FVBox20.setLeft(0);
        FVBox20.setTop(0);
        FVBox20.setWidth(550);
        FVBox20.setHeight(73);
        FVBox20.setBorderStyle("stNone");
        FVBox20.setPaddingTop(0);
        FVBox20.setPaddingLeft(0);
        FVBox20.setPaddingRight(0);
        FVBox20.setPaddingBottom(0);
        FVBox20.setMarginTop(0);
        FVBox20.setMarginLeft(0);
        FVBox20.setMarginRight(0);
        FVBox20.setMarginBottom(0);
        FVBox20.setSpacing(5);
        FVBox20.setFlexVflex("ftMin");
        FVBox20.setFlexHflex("ftTrue");
        FVBox20.setScrollable(false);
        FVBox20.setBoxShadowConfigHorizontalLength(10);
        FVBox20.setBoxShadowConfigVerticalLength(10);
        FVBox20.setBoxShadowConfigBlurRadius(5);
        FVBox20.setBoxShadowConfigSpreadRadius(0);
        FVBox20.setBoxShadowConfigShadowColor("clBlack");
        FVBox20.setBoxShadowConfigOpacity(75);
        FVBox19.addChildren(FVBox20);
        FVBox20.applyProperties();
    }

    public TFLabel lblDescServicVal = new TFLabel();

    private void init_lblDescServicVal() {
        lblDescServicVal.setName("lblDescServicVal");
        lblDescServicVal.setLeft(0);
        lblDescServicVal.setTop(0);
        lblDescServicVal.setWidth(180);
        lblDescServicVal.setHeight(24);
        lblDescServicVal.setCaption("Descri\u00E7\u00E3o Servi\u00E7o....");
        lblDescServicVal.setFontColor("clHotLight");
        lblDescServicVal.setFontSize(-20);
        lblDescServicVal.setFontName("Tahoma");
        lblDescServicVal.setFontStyle("[]");
        lblDescServicVal.setVisible(false);
        lblDescServicVal.setVerticalAlignment("taVerticalCenter");
        lblDescServicVal.setWordBreak(false);
        FVBox20.addChildren(lblDescServicVal);
        lblDescServicVal.applyProperties();
    }

    public TFVBox FVBox61 = new TFVBox();

    private void init_FVBox61() {
        FVBox61.setName("FVBox61");
        FVBox61.setLeft(0);
        FVBox61.setTop(25);
        FVBox61.setWidth(207);
        FVBox61.setHeight(41);
        FVBox61.setBorderStyle("stNone");
        FVBox61.setPaddingTop(0);
        FVBox61.setPaddingLeft(0);
        FVBox61.setPaddingRight(0);
        FVBox61.setPaddingBottom(0);
        FVBox61.setMarginTop(0);
        FVBox61.setMarginLeft(0);
        FVBox61.setMarginRight(0);
        FVBox61.setMarginBottom(0);
        FVBox61.setSpacing(5);
        FVBox61.setFlexVflex("ftTrue");
        FVBox61.setFlexHflex("ftTrue");
        FVBox61.setScrollable(false);
        FVBox61.setBoxShadowConfigHorizontalLength(10);
        FVBox61.setBoxShadowConfigVerticalLength(10);
        FVBox61.setBoxShadowConfigBlurRadius(5);
        FVBox61.setBoxShadowConfigSpreadRadius(0);
        FVBox61.setBoxShadowConfigShadowColor("clBlack");
        FVBox61.setBoxShadowConfigOpacity(75);
        FVBox20.addChildren(FVBox61);
        FVBox61.applyProperties();
    }

    public TFLabel FLabel39 = new TFLabel();

    private void init_FLabel39() {
        FLabel39.setName("FLabel39");
        FLabel39.setLeft(0);
        FLabel39.setTop(0);
        FLabel39.setWidth(63);
        FLabel39.setHeight(13);
        FLabel39.setCaption("Como Cobrar");
        FLabel39.setFontColor("clWindowText");
        FLabel39.setFontSize(-11);
        FLabel39.setFontName("Tahoma");
        FLabel39.setFontStyle("[]");
        FLabel39.setVerticalAlignment("taVerticalCenter");
        FLabel39.setWordBreak(false);
        FVBox61.addChildren(FLabel39);
        FLabel39.applyProperties();
    }

    public TFCombo FCombo3 = new TFCombo();

    private void init_FCombo3() {
        FCombo3.setName("FCombo3");
        FCombo3.setLeft(0);
        FCombo3.setTop(14);
        FCombo3.setWidth(189);
        FCombo3.setHeight(21);
        FCombo3.setTable(tbServicos);
        FCombo3.setFieldName("COMO_COBRAR");
        FCombo3.setFlex(true);
        FCombo3.setListOptions("Por Tempo=T;Por Pre\u00E7o Tabelado=P;N\u00E3o Cobrar=Z");
        FCombo3.setReadOnly(true);
        FCombo3.setRequired(false);
        FCombo3.setPrompt("Selecione");
        FCombo3.setConstraintCheckWhen("cwImmediate");
        FCombo3.setConstraintCheckType("ctExpression");
        FCombo3.setConstraintFocusOnError(false);
        FCombo3.setConstraintEnableUI(true);
        FCombo3.setConstraintEnabled(false);
        FCombo3.setConstraintFormCheck(true);
        FCombo3.setClearOnDelKey(true);
        FCombo3.setUseClearButton(false);
        FCombo3.setHideClearButtonOnNullValue(true);
        FVBox61.addChildren(FCombo3);
        FCombo3.applyProperties();
        addValidatable(FCombo3);
    }

    public TFLabel lblTempo = new TFLabel();

    private void init_lblTempo() {
        lblTempo.setName("lblTempo");
        lblTempo.setLeft(0);
        lblTempo.setTop(74);
        lblTempo.setWidth(50);
        lblTempo.setHeight(19);
        lblTempo.setCaption("Tempo");
        lblTempo.setFontColor("clHotLight");
        lblTempo.setFontSize(-16);
        lblTempo.setFontName("Tahoma");
        lblTempo.setFontStyle("[]");
        lblTempo.setVerticalAlignment("taVerticalCenter");
        lblTempo.setWordBreak(false);
        FVBox19.addChildren(lblTempo);
        lblTempo.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(94);
        FHBox9.setWidth(551);
        FHBox9.setHeight(65);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(5);
        FHBox9.setFlexVflex("ftMin");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FVBox19.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFVBox FVBox21 = new TFVBox();

    private void init_FVBox21() {
        FVBox21.setName("FVBox21");
        FVBox21.setLeft(0);
        FVBox21.setTop(0);
        FVBox21.setWidth(175);
        FVBox21.setHeight(61);
        FVBox21.setBorderStyle("stNone");
        FVBox21.setPaddingTop(0);
        FVBox21.setPaddingLeft(0);
        FVBox21.setPaddingRight(0);
        FVBox21.setPaddingBottom(0);
        FVBox21.setMarginTop(0);
        FVBox21.setMarginLeft(0);
        FVBox21.setMarginRight(0);
        FVBox21.setMarginBottom(0);
        FVBox21.setSpacing(5);
        FVBox21.setFlexVflex("ftMin");
        FVBox21.setFlexHflex("ftTrue");
        FVBox21.setScrollable(false);
        FVBox21.setBoxShadowConfigHorizontalLength(10);
        FVBox21.setBoxShadowConfigVerticalLength(10);
        FVBox21.setBoxShadowConfigBlurRadius(5);
        FVBox21.setBoxShadowConfigSpreadRadius(0);
        FVBox21.setBoxShadowConfigShadowColor("clBlack");
        FVBox21.setBoxShadowConfigOpacity(75);
        FHBox9.addChildren(FVBox21);
        FVBox21.applyProperties();
    }

    public TFLabel FLabel10 = new TFLabel();

    private void init_FLabel10() {
        FLabel10.setName("FLabel10");
        FLabel10.setLeft(0);
        FLabel10.setTop(0);
        FLabel10.setWidth(59);
        FLabel10.setHeight(13);
        FLabel10.setCaption("TMO Padr\u00E3o");
        FLabel10.setFontColor("clWindowText");
        FLabel10.setFontSize(-11);
        FLabel10.setFontName("Tahoma");
        FLabel10.setFontStyle("[]");
        FLabel10.setVerticalAlignment("taVerticalCenter");
        FLabel10.setWordBreak(false);
        FVBox21.addChildren(FLabel10);
        FLabel10.applyProperties();
    }

    public TFDecimal FDecTmoP = new TFDecimal();

    private void init_FDecTmoP() {
        FDecTmoP.setName("FDecTmoP");
        FDecTmoP.setLeft(0);
        FDecTmoP.setTop(14);
        FDecTmoP.setWidth(170);
        FDecTmoP.setHeight(24);
        FDecTmoP.setTable(tbServicos);
        FDecTmoP.setFieldName("TEMPO_PADRAO");
        FDecTmoP.setFlex(true);
        FDecTmoP.setRequired(true);
        FDecTmoP.setConstraintMessage("N\u00E3o permitido TMO Padr\u00E3o negativo");
        FDecTmoP.setConstraintCheckWhen("cwImmediate");
        FDecTmoP.setConstraintCheckType("ctExpression");
        FDecTmoP.setConstraintFocusOnError(false);
        FDecTmoP.setConstraintEnableUI(false);
        FDecTmoP.setConstraintEnabled(false);
        FDecTmoP.setConstraintFormCheck(false);
        FDecTmoP.setMaxlength(0);
        FDecTmoP.setPrecision(5);
        FDecTmoP.setFontColor("clWindowText");
        FDecTmoP.setFontSize(-13);
        FDecTmoP.setFontName("Tahoma");
        FDecTmoP.setFontStyle("[]");
        FDecTmoP.setAlignment("taRightJustify");
        FVBox21.addChildren(FDecTmoP);
        FDecTmoP.applyProperties();
        addValidatable(FDecTmoP);
    }

    public TFVBox FVBox22 = new TFVBox();

    private void init_FVBox22() {
        FVBox22.setName("FVBox22");
        FVBox22.setLeft(175);
        FVBox22.setTop(0);
        FVBox22.setWidth(177);
        FVBox22.setHeight(61);
        FVBox22.setBorderStyle("stNone");
        FVBox22.setPaddingTop(0);
        FVBox22.setPaddingLeft(0);
        FVBox22.setPaddingRight(0);
        FVBox22.setPaddingBottom(0);
        FVBox22.setMarginTop(0);
        FVBox22.setMarginLeft(0);
        FVBox22.setMarginRight(0);
        FVBox22.setMarginBottom(0);
        FVBox22.setSpacing(5);
        FVBox22.setFlexVflex("ftMin");
        FVBox22.setFlexHflex("ftTrue");
        FVBox22.setScrollable(false);
        FVBox22.setBoxShadowConfigHorizontalLength(10);
        FVBox22.setBoxShadowConfigVerticalLength(10);
        FVBox22.setBoxShadowConfigBlurRadius(5);
        FVBox22.setBoxShadowConfigSpreadRadius(0);
        FVBox22.setBoxShadowConfigShadowColor("clBlack");
        FVBox22.setBoxShadowConfigOpacity(75);
        FHBox9.addChildren(FVBox22);
        FVBox22.applyProperties();
    }

    public TFLabel FLabel11 = new TFLabel();

    private void init_FLabel11() {
        FLabel11.setName("FLabel11");
        FLabel11.setLeft(0);
        FLabel11.setTop(0);
        FLabel11.setWidth(80);
        FLabel11.setHeight(13);
        FLabel11.setCaption("Agenda Premium");
        FLabel11.setFontColor("clWindowText");
        FLabel11.setFontSize(-11);
        FLabel11.setFontName("Tahoma");
        FLabel11.setFontStyle("[]");
        FLabel11.setVerticalAlignment("taVerticalCenter");
        FLabel11.setWordBreak(false);
        FVBox22.addChildren(FLabel11);
        FLabel11.applyProperties();
    }

    public TFDecimal FDecAgend = new TFDecimal();

    private void init_FDecAgend() {
        FDecAgend.setName("FDecAgend");
        FDecAgend.setLeft(0);
        FDecAgend.setTop(14);
        FDecAgend.setWidth(170);
        FDecAgend.setHeight(24);
        FDecAgend.setTable(tbServicos);
        FDecAgend.setFieldName("TEMPO_AGENDA");
        FDecAgend.setFlex(true);
        FDecAgend.setRequired(false);
        FDecAgend.setConstraintExpression("value < 0");
        FDecAgend.setConstraintMessage("N\u00E3o permitido Agenda Premium Negativo");
        FDecAgend.setConstraintCheckWhen("cwImmediate");
        FDecAgend.setConstraintCheckType("ctExpression");
        FDecAgend.setConstraintFocusOnError(false);
        FDecAgend.setConstraintEnableUI(false);
        FDecAgend.setConstraintEnabled(false);
        FDecAgend.setConstraintFormCheck(false);
        FDecAgend.setMaxlength(0);
        FDecAgend.setPrecision(2);
        FDecAgend.setEnabled(false);
        FDecAgend.setFontColor("clWindowText");
        FDecAgend.setFontSize(-13);
        FDecAgend.setFontName("Tahoma");
        FDecAgend.setFontStyle("[]");
        FDecAgend.setAlignment("taRightJustify");
        FVBox22.addChildren(FDecAgend);
        FDecAgend.applyProperties();
        addValidatable(FDecAgend);
    }

    public TFVBox FVBox23 = new TFVBox();

    private void init_FVBox23() {
        FVBox23.setName("FVBox23");
        FVBox23.setLeft(352);
        FVBox23.setTop(0);
        FVBox23.setWidth(181);
        FVBox23.setHeight(61);
        FVBox23.setBorderStyle("stNone");
        FVBox23.setPaddingTop(0);
        FVBox23.setPaddingLeft(0);
        FVBox23.setPaddingRight(0);
        FVBox23.setPaddingBottom(0);
        FVBox23.setMarginTop(0);
        FVBox23.setMarginLeft(0);
        FVBox23.setMarginRight(0);
        FVBox23.setMarginBottom(0);
        FVBox23.setSpacing(5);
        FVBox23.setFlexVflex("ftMin");
        FVBox23.setFlexHflex("ftTrue");
        FVBox23.setScrollable(false);
        FVBox23.setBoxShadowConfigHorizontalLength(10);
        FVBox23.setBoxShadowConfigVerticalLength(10);
        FVBox23.setBoxShadowConfigBlurRadius(5);
        FVBox23.setBoxShadowConfigSpreadRadius(0);
        FVBox23.setBoxShadowConfigShadowColor("clBlack");
        FVBox23.setBoxShadowConfigOpacity(75);
        FHBox9.addChildren(FVBox23);
        FVBox23.applyProperties();
    }

    public TFLabel FLabel12 = new TFLabel();

    private void init_FLabel12() {
        FLabel12.setName("FLabel12");
        FLabel12.setLeft(0);
        FLabel12.setTop(0);
        FLabel12.setWidth(41);
        FLabel12.setHeight(13);
        FLabel12.setCaption("Duo TEC");
        FLabel12.setFontColor("clWindowText");
        FLabel12.setFontSize(-11);
        FLabel12.setFontName("Tahoma");
        FLabel12.setFontStyle("[]");
        FLabel12.setVerticalAlignment("taVerticalCenter");
        FLabel12.setWordBreak(false);
        FVBox23.addChildren(FLabel12);
        FLabel12.applyProperties();
    }

    public TFDecimal FDecDuoTec = new TFDecimal();

    private void init_FDecDuoTec() {
        FDecDuoTec.setName("FDecDuoTec");
        FDecDuoTec.setLeft(0);
        FDecDuoTec.setTop(14);
        FDecDuoTec.setWidth(170);
        FDecDuoTec.setHeight(24);
        FDecDuoTec.setTable(tbServicos);
        FDecDuoTec.setFieldName("DUA_TEC");
        FDecDuoTec.setFlex(true);
        FDecDuoTec.setRequired(false);
        FDecDuoTec.setConstraintExpression("value < 0");
        FDecDuoTec.setConstraintMessage("N\u00E3o permitido Dua Tec Negativo");
        FDecDuoTec.setConstraintCheckWhen("cwImmediate");
        FDecDuoTec.setConstraintCheckType("ctExpression");
        FDecDuoTec.setConstraintFocusOnError(false);
        FDecDuoTec.setConstraintEnableUI(true);
        FDecDuoTec.setConstraintEnabled(true);
        FDecDuoTec.setConstraintFormCheck(true);
        FDecDuoTec.setMaxlength(0);
        FDecDuoTec.setPrecision(2);
        FDecDuoTec.setFontColor("clWindowText");
        FDecDuoTec.setFontSize(-13);
        FDecDuoTec.setFontName("Tahoma");
        FDecDuoTec.setFontStyle("[]");
        FDecDuoTec.setAlignment("taRightJustify");
        FVBox23.addChildren(FDecDuoTec);
        FDecDuoTec.applyProperties();
        addValidatable(FDecDuoTec);
    }

    public TFLabel FLabel13 = new TFLabel();

    private void init_FLabel13() {
        FLabel13.setName("FLabel13");
        FLabel13.setLeft(0);
        FLabel13.setTop(160);
        FLabel13.setWidth(52);
        FLabel13.setHeight(19);
        FLabel13.setCaption("Valores");
        FLabel13.setFontColor("clHotLight");
        FLabel13.setFontSize(-16);
        FLabel13.setFontName("Tahoma");
        FLabel13.setFontStyle("[]");
        FLabel13.setVerticalAlignment("taVerticalCenter");
        FLabel13.setWordBreak(false);
        FVBox19.addChildren(FLabel13);
        FLabel13.applyProperties();
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(0);
        FHBox15.setTop(180);
        FHBox15.setWidth(552);
        FHBox15.setHeight(65);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(5);
        FHBox15.setFlexVflex("ftMin");
        FHBox15.setFlexHflex("ftTrue");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        FVBox19.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFVBox FVBox24 = new TFVBox();

    private void init_FVBox24() {
        FVBox24.setName("FVBox24");
        FVBox24.setLeft(0);
        FVBox24.setTop(0);
        FVBox24.setWidth(172);
        FVBox24.setHeight(61);
        FVBox24.setBorderStyle("stNone");
        FVBox24.setPaddingTop(0);
        FVBox24.setPaddingLeft(0);
        FVBox24.setPaddingRight(0);
        FVBox24.setPaddingBottom(0);
        FVBox24.setMarginTop(0);
        FVBox24.setMarginLeft(0);
        FVBox24.setMarginRight(0);
        FVBox24.setMarginBottom(0);
        FVBox24.setSpacing(5);
        FVBox24.setFlexVflex("ftMin");
        FVBox24.setFlexHflex("ftTrue");
        FVBox24.setScrollable(false);
        FVBox24.setBoxShadowConfigHorizontalLength(10);
        FVBox24.setBoxShadowConfigVerticalLength(10);
        FVBox24.setBoxShadowConfigBlurRadius(5);
        FVBox24.setBoxShadowConfigSpreadRadius(0);
        FVBox24.setBoxShadowConfigShadowColor("clBlack");
        FVBox24.setBoxShadowConfigOpacity(75);
        FHBox15.addChildren(FVBox24);
        FVBox24.applyProperties();
    }

    public TFLabel FLabel14 = new TFLabel();

    private void init_FLabel14() {
        FLabel14.setName("FLabel14");
        FLabel14.setLeft(0);
        FLabel14.setTop(0);
        FLabel14.setWidth(60);
        FLabel14.setHeight(13);
        FLabel14.setCaption("Pre\u00E7o Venda");
        FLabel14.setFontColor("clWindowText");
        FLabel14.setFontSize(-11);
        FLabel14.setFontName("Tahoma");
        FLabel14.setFontStyle("[]");
        FLabel14.setVerticalAlignment("taVerticalCenter");
        FLabel14.setWordBreak(false);
        FVBox24.addChildren(FLabel14);
        FLabel14.applyProperties();
    }

    public TFDecimal FDecPrecVen = new TFDecimal();

    private void init_FDecPrecVen() {
        FDecPrecVen.setName("FDecPrecVen");
        FDecPrecVen.setLeft(0);
        FDecPrecVen.setTop(14);
        FDecPrecVen.setWidth(170);
        FDecPrecVen.setHeight(24);
        FDecPrecVen.setTable(tbServicos);
        FDecPrecVen.setFieldName("PRECO_VENDA");
        FDecPrecVen.setFlex(true);
        FDecPrecVen.setRequired(false);
        FDecPrecVen.setConstraintExpression("value < 0");
        FDecPrecVen.setConstraintMessage("N\u00E3o permitido valor abaixo de Zero");
        FDecPrecVen.setConstraintCheckWhen("cwImmediate");
        FDecPrecVen.setConstraintCheckType("ctExpression");
        FDecPrecVen.setConstraintFocusOnError(false);
        FDecPrecVen.setConstraintEnableUI(true);
        FDecPrecVen.setConstraintEnabled(true);
        FDecPrecVen.setConstraintFormCheck(true);
        FDecPrecVen.setMaxlength(0);
        FDecPrecVen.setPrecision(2);
        FDecPrecVen.setFontColor("clWindowText");
        FDecPrecVen.setFontSize(-13);
        FDecPrecVen.setFontName("Tahoma");
        FDecPrecVen.setFontStyle("[]");
        FDecPrecVen.setAlignment("taRightJustify");
        FVBox24.addChildren(FDecPrecVen);
        FDecPrecVen.applyProperties();
        addValidatable(FDecPrecVen);
    }

    public TFVBox FVBox25 = new TFVBox();

    private void init_FVBox25() {
        FVBox25.setName("FVBox25");
        FVBox25.setLeft(172);
        FVBox25.setTop(0);
        FVBox25.setWidth(179);
        FVBox25.setHeight(61);
        FVBox25.setBorderStyle("stNone");
        FVBox25.setPaddingTop(0);
        FVBox25.setPaddingLeft(0);
        FVBox25.setPaddingRight(0);
        FVBox25.setPaddingBottom(0);
        FVBox25.setMarginTop(0);
        FVBox25.setMarginLeft(0);
        FVBox25.setMarginRight(0);
        FVBox25.setMarginBottom(0);
        FVBox25.setSpacing(5);
        FVBox25.setFlexVflex("ftMin");
        FVBox25.setFlexHflex("ftTrue");
        FVBox25.setScrollable(false);
        FVBox25.setBoxShadowConfigHorizontalLength(10);
        FVBox25.setBoxShadowConfigVerticalLength(10);
        FVBox25.setBoxShadowConfigBlurRadius(5);
        FVBox25.setBoxShadowConfigSpreadRadius(0);
        FVBox25.setBoxShadowConfigShadowColor("clBlack");
        FVBox25.setBoxShadowConfigOpacity(75);
        FHBox15.addChildren(FVBox25);
        FVBox25.applyProperties();
    }

    public TFLabel FLabel15 = new TFLabel();

    private void init_FLabel15() {
        FLabel15.setName("FLabel15");
        FLabel15.setLeft(0);
        FLabel15.setTop(0);
        FLabel15.setWidth(58);
        FLabel15.setHeight(13);
        FLabel15.setCaption("Pre\u00E7o Custo");
        FLabel15.setFontColor("clWindowText");
        FLabel15.setFontSize(-11);
        FLabel15.setFontName("Tahoma");
        FLabel15.setFontStyle("[]");
        FLabel15.setVerticalAlignment("taVerticalCenter");
        FLabel15.setWordBreak(false);
        FVBox25.addChildren(FLabel15);
        FLabel15.applyProperties();
    }

    public TFDecimal FDecPrecCus = new TFDecimal();

    private void init_FDecPrecCus() {
        FDecPrecCus.setName("FDecPrecCus");
        FDecPrecCus.setLeft(0);
        FDecPrecCus.setTop(14);
        FDecPrecCus.setWidth(170);
        FDecPrecCus.setHeight(24);
        FDecPrecCus.setTable(tbServicos);
        FDecPrecCus.setFieldName("PRECO_CUSTO");
        FDecPrecCus.setFlex(true);
        FDecPrecCus.setRequired(false);
        FDecPrecCus.setConstraintExpression("value < 0");
        FDecPrecCus.setConstraintMessage("Pre\u00E7o custo n\u00E3o permitido valor negativo");
        FDecPrecCus.setConstraintCheckWhen("cwImmediate");
        FDecPrecCus.setConstraintCheckType("ctExpression");
        FDecPrecCus.setConstraintFocusOnError(false);
        FDecPrecCus.setConstraintEnableUI(true);
        FDecPrecCus.setConstraintEnabled(true);
        FDecPrecCus.setConstraintFormCheck(true);
        FDecPrecCus.setMaxlength(0);
        FDecPrecCus.setPrecision(2);
        FDecPrecCus.setFontColor("clWindowText");
        FDecPrecCus.setFontSize(-13);
        FDecPrecCus.setFontName("Tahoma");
        FDecPrecCus.setFontStyle("[]");
        FDecPrecCus.setAlignment("taRightJustify");
        FVBox25.addChildren(FDecPrecCus);
        FDecPrecCus.applyProperties();
        addValidatable(FDecPrecCus);
    }

    public TFLabel FLabel16 = new TFLabel();

    private void init_FLabel16() {
        FLabel16.setName("FLabel16");
        FLabel16.setLeft(0);
        FLabel16.setTop(246);
        FLabel16.setWidth(68);
        FLabel16.setHeight(19);
        FLabel16.setCaption("Comiss\u00E3o");
        FLabel16.setFontColor("clHotLight");
        FLabel16.setFontSize(-16);
        FLabel16.setFontName("Tahoma");
        FLabel16.setFontStyle("[]");
        FLabel16.setVerticalAlignment("taVerticalCenter");
        FLabel16.setWordBreak(false);
        FVBox19.addChildren(FLabel16);
        FLabel16.applyProperties();
    }

    public TFHBox FHBox17 = new TFHBox();

    private void init_FHBox17() {
        FHBox17.setName("FHBox17");
        FHBox17.setLeft(0);
        FHBox17.setTop(266);
        FHBox17.setWidth(552);
        FHBox17.setHeight(49);
        FHBox17.setBorderStyle("stNone");
        FHBox17.setPaddingTop(0);
        FHBox17.setPaddingLeft(0);
        FHBox17.setPaddingRight(0);
        FHBox17.setPaddingBottom(0);
        FHBox17.setMarginTop(0);
        FHBox17.setMarginLeft(0);
        FHBox17.setMarginRight(0);
        FHBox17.setMarginBottom(0);
        FHBox17.setSpacing(5);
        FHBox17.setFlexVflex("ftMin");
        FHBox17.setFlexHflex("ftTrue");
        FHBox17.setScrollable(false);
        FHBox17.setBoxShadowConfigHorizontalLength(10);
        FHBox17.setBoxShadowConfigVerticalLength(10);
        FHBox17.setBoxShadowConfigBlurRadius(5);
        FHBox17.setBoxShadowConfigSpreadRadius(0);
        FHBox17.setBoxShadowConfigShadowColor("clBlack");
        FHBox17.setBoxShadowConfigOpacity(75);
        FHBox17.setVAlign("tvTop");
        FVBox19.addChildren(FHBox17);
        FHBox17.applyProperties();
    }

    public TFCombo FCombo2 = new TFCombo();

    private void init_FCombo2() {
        FCombo2.setName("FCombo2");
        FCombo2.setLeft(0);
        FCombo2.setTop(0);
        FCombo2.setWidth(145);
        FCombo2.setHeight(21);
        FCombo2.setTable(tbServicos);
        FCombo2.setFieldName("TIPO_COMISSAO_AGENDA");
        FCombo2.setFlex(true);
        FCombo2.setListOptions("Fixo Percentual=F; Pre\u00E7o Fixo=P");
        FCombo2.setReadOnly(true);
        FCombo2.setRequired(false);
        FCombo2.setPrompt("Sem Comiss\u00E3o");
        FCombo2.setConstraintCheckWhen("cwImmediate");
        FCombo2.setConstraintCheckType("ctExpression");
        FCombo2.setConstraintFocusOnError(false);
        FCombo2.setConstraintEnableUI(true);
        FCombo2.setConstraintEnabled(false);
        FCombo2.setConstraintFormCheck(true);
        FCombo2.setClearOnDelKey(true);
        FCombo2.setUseClearButton(false);
        FCombo2.setHideClearButtonOnNullValue(true);
        FHBox17.addChildren(FCombo2);
        FCombo2.applyProperties();
        addValidatable(FCombo2);
    }

    public TFDecimal FDecimal1 = new TFDecimal();

    private void init_FDecimal1() {
        FDecimal1.setName("FDecimal1");
        FDecimal1.setLeft(145);
        FDecimal1.setTop(0);
        FDecimal1.setWidth(121);
        FDecimal1.setHeight(24);
        FDecimal1.setTable(tbServicos);
        FDecimal1.setFieldName("VALOR_COMISSAO_AGENDA");
        FDecimal1.setFlex(true);
        FDecimal1.setRequired(false);
        FDecimal1.setPrompt("Valor Comiss\u00E3o");
        FDecimal1.setConstraintMessage("N\u00E3o permitido Valor comiss\u00E3o Negativo");
        FDecimal1.setConstraintCheckWhen("cwImmediate");
        FDecimal1.setConstraintCheckType("ctExpression");
        FDecimal1.setConstraintFocusOnError(false);
        FDecimal1.setConstraintEnableUI(true);
        FDecimal1.setConstraintEnabled(false);
        FDecimal1.setConstraintFormCheck(true);
        FDecimal1.setMaxlength(0);
        FDecimal1.setPrecision(2);
        FDecimal1.setFontColor("clWindowText");
        FDecimal1.setFontSize(-13);
        FDecimal1.setFontName("Tahoma");
        FDecimal1.setFontStyle("[]");
        FDecimal1.setAlignment("taRightJustify");
        FHBox17.addChildren(FDecimal1);
        FDecimal1.applyProperties();
        addValidatable(FDecimal1);
    }

    public TFTabsheet FTabImposto = new TFTabsheet();

    private void init_FTabImposto() {
        FTabImposto.setName("FTabImposto");
        FTabImposto.setCaption("Imposto");
        FTabImposto.setVisible(true);
        FTabImposto.setClosable(false);
        pageControlServico.addChildren(FTabImposto);
        FTabImposto.applyProperties();
    }

    public TFVBox vBoxImposto = new TFVBox();

    private void init_vBoxImposto() {
        vBoxImposto.setName("vBoxImposto");
        vBoxImposto.setLeft(0);
        vBoxImposto.setTop(0);
        vBoxImposto.setWidth(546);
        vBoxImposto.setHeight(772);
        vBoxImposto.setAlign("alClient");
        vBoxImposto.setBorderStyle("stNone");
        vBoxImposto.setPaddingTop(0);
        vBoxImposto.setPaddingLeft(5);
        vBoxImposto.setPaddingRight(5);
        vBoxImposto.setPaddingBottom(0);
        vBoxImposto.setMarginTop(0);
        vBoxImposto.setMarginLeft(0);
        vBoxImposto.setMarginRight(0);
        vBoxImposto.setMarginBottom(0);
        vBoxImposto.setSpacing(5);
        vBoxImposto.setFlexVflex("ftTrue");
        vBoxImposto.setFlexHflex("ftTrue");
        vBoxImposto.setScrollable(true);
        vBoxImposto.setBoxShadowConfigHorizontalLength(10);
        vBoxImposto.setBoxShadowConfigVerticalLength(10);
        vBoxImposto.setBoxShadowConfigBlurRadius(5);
        vBoxImposto.setBoxShadowConfigSpreadRadius(0);
        vBoxImposto.setBoxShadowConfigShadowColor("clBlack");
        vBoxImposto.setBoxShadowConfigOpacity(75);
        FTabImposto.addChildren(vBoxImposto);
        vBoxImposto.applyProperties();
    }

    public TFVBox FVBox27 = new TFVBox();

    private void init_FVBox27() {
        FVBox27.setName("FVBox27");
        FVBox27.setLeft(0);
        FVBox27.setTop(0);
        FVBox27.setWidth(563);
        FVBox27.setHeight(124);
        FVBox27.setBorderStyle("stNone");
        FVBox27.setPaddingTop(0);
        FVBox27.setPaddingLeft(0);
        FVBox27.setPaddingRight(0);
        FVBox27.setPaddingBottom(20);
        FVBox27.setMarginTop(0);
        FVBox27.setMarginLeft(0);
        FVBox27.setMarginRight(0);
        FVBox27.setMarginBottom(0);
        FVBox27.setSpacing(5);
        FVBox27.setFlexVflex("ftMin");
        FVBox27.setFlexHflex("ftTrue");
        FVBox27.setScrollable(false);
        FVBox27.setBoxShadowConfigHorizontalLength(10);
        FVBox27.setBoxShadowConfigVerticalLength(10);
        FVBox27.setBoxShadowConfigBlurRadius(5);
        FVBox27.setBoxShadowConfigSpreadRadius(0);
        FVBox27.setBoxShadowConfigShadowColor("clBlack");
        FVBox27.setBoxShadowConfigOpacity(75);
        vBoxImposto.addChildren(FVBox27);
        FVBox27.applyProperties();
    }

    public TFLabel lblDescServicImp = new TFLabel();

    private void init_lblDescServicImp() {
        lblDescServicImp.setName("lblDescServicImp");
        lblDescServicImp.setLeft(0);
        lblDescServicImp.setTop(0);
        lblDescServicImp.setWidth(180);
        lblDescServicImp.setHeight(24);
        lblDescServicImp.setCaption("Descri\u00E7\u00E3o Servi\u00E7o....");
        lblDescServicImp.setFontColor("clHotLight");
        lblDescServicImp.setFontSize(-20);
        lblDescServicImp.setFontName("Tahoma");
        lblDescServicImp.setFontStyle("[]");
        lblDescServicImp.setVisible(false);
        lblDescServicImp.setVerticalAlignment("taVerticalCenter");
        lblDescServicImp.setWordBreak(false);
        FVBox27.addChildren(lblDescServicImp);
        lblDescServicImp.applyProperties();
    }

    public TFHBox FHBox35 = new TFHBox();

    private void init_FHBox35() {
        FHBox35.setName("FHBox35");
        FHBox35.setLeft(0);
        FHBox35.setTop(25);
        FHBox35.setWidth(562);
        FHBox35.setHeight(95);
        FHBox35.setBorderStyle("stNone");
        FHBox35.setPaddingTop(0);
        FHBox35.setPaddingLeft(0);
        FHBox35.setPaddingRight(0);
        FHBox35.setPaddingBottom(0);
        FHBox35.setMarginTop(0);
        FHBox35.setMarginLeft(0);
        FHBox35.setMarginRight(0);
        FHBox35.setMarginBottom(0);
        FHBox35.setSpacing(1);
        FHBox35.setFlexVflex("ftFalse");
        FHBox35.setFlexHflex("ftTrue");
        FHBox35.setScrollable(false);
        FHBox35.setBoxShadowConfigHorizontalLength(10);
        FHBox35.setBoxShadowConfigVerticalLength(10);
        FHBox35.setBoxShadowConfigBlurRadius(5);
        FHBox35.setBoxShadowConfigSpreadRadius(0);
        FHBox35.setBoxShadowConfigShadowColor("clBlack");
        FHBox35.setBoxShadowConfigOpacity(75);
        FHBox35.setVAlign("tvTop");
        FVBox27.addChildren(FHBox35);
        FHBox35.applyProperties();
    }

    public TFVBox FVBox66 = new TFVBox();

    private void init_FVBox66() {
        FVBox66.setName("FVBox66");
        FVBox66.setLeft(0);
        FVBox66.setTop(0);
        FVBox66.setWidth(276);
        FVBox66.setHeight(88);
        FVBox66.setBorderStyle("stNone");
        FVBox66.setPaddingTop(0);
        FVBox66.setPaddingLeft(0);
        FVBox66.setPaddingRight(0);
        FVBox66.setPaddingBottom(0);
        FVBox66.setMarginTop(0);
        FVBox66.setMarginLeft(0);
        FVBox66.setMarginRight(0);
        FVBox66.setMarginBottom(0);
        FVBox66.setSpacing(7);
        FVBox66.setFlexVflex("ftTrue");
        FVBox66.setFlexHflex("ftMin");
        FVBox66.setScrollable(false);
        FVBox66.setBoxShadowConfigHorizontalLength(10);
        FVBox66.setBoxShadowConfigVerticalLength(10);
        FVBox66.setBoxShadowConfigBlurRadius(5);
        FVBox66.setBoxShadowConfigSpreadRadius(0);
        FVBox66.setBoxShadowConfigShadowColor("clBlack");
        FVBox66.setBoxShadowConfigOpacity(75);
        FHBox35.addChildren(FVBox66);
        FVBox66.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(273);
        FHBox3.setHeight(37);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftMin");
        FHBox3.setFlexHflex("ftMin");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FVBox66.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFVBox FVBox68 = new TFVBox();

    private void init_FVBox68() {
        FVBox68.setName("FVBox68");
        FVBox68.setLeft(0);
        FVBox68.setTop(0);
        FVBox68.setWidth(166);
        FVBox68.setHeight(33);
        FVBox68.setBorderStyle("stNone");
        FVBox68.setPaddingTop(3);
        FVBox68.setPaddingLeft(0);
        FVBox68.setPaddingRight(0);
        FVBox68.setPaddingBottom(0);
        FVBox68.setMarginTop(0);
        FVBox68.setMarginLeft(0);
        FVBox68.setMarginRight(0);
        FVBox68.setMarginBottom(0);
        FVBox68.setSpacing(1);
        FVBox68.setFlexVflex("ftTrue");
        FVBox68.setFlexHflex("ftMin");
        FVBox68.setScrollable(false);
        FVBox68.setBoxShadowConfigHorizontalLength(10);
        FVBox68.setBoxShadowConfigVerticalLength(10);
        FVBox68.setBoxShadowConfigBlurRadius(5);
        FVBox68.setBoxShadowConfigSpreadRadius(0);
        FVBox68.setBoxShadowConfigShadowColor("clBlack");
        FVBox68.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox68);
        FVBox68.applyProperties();
    }

    public TFCheckBox ckbRetemIrrfCad = new TFCheckBox();

    private void init_ckbRetemIrrfCad() {
        ckbRetemIrrfCad.setName("ckbRetemIrrfCad");
        ckbRetemIrrfCad.setLeft(0);
        ckbRetemIrrfCad.setTop(0);
        ckbRetemIrrfCad.setWidth(165);
        ckbRetemIrrfCad.setHeight(17);
        ckbRetemIrrfCad.setCaption("Retem IRRF com Aliquota de: ");
        ckbRetemIrrfCad.setFontColor("clWindowText");
        ckbRetemIrrfCad.setFontSize(-11);
        ckbRetemIrrfCad.setFontName("Tahoma");
        ckbRetemIrrfCad.setFontStyle("[]");
        ckbRetemIrrfCad.setTable(tbServicos);
        ckbRetemIrrfCad.setFieldName("RETEM_IRRF");
        ckbRetemIrrfCad.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            ckbRetemIrrfCadCheck(event);
            processarFlow("FrmServicos", "ckbRetemIrrfCad", "OnCheck");
        });
        ckbRetemIrrfCad.setVerticalAlignment("taAlignTop");
        FVBox68.addChildren(ckbRetemIrrfCad);
        ckbRetemIrrfCad.applyProperties();
    }

    public TFDecimal FDecAliqIRRF = new TFDecimal();

    private void init_FDecAliqIRRF() {
        FDecAliqIRRF.setName("FDecAliqIRRF");
        FDecAliqIRRF.setLeft(166);
        FDecAliqIRRF.setTop(0);
        FDecAliqIRRF.setWidth(65);
        FDecAliqIRRF.setHeight(24);
        FDecAliqIRRF.setTable(tbServicos);
        FDecAliqIRRF.setFieldName("ALIQ_IRRF");
        FDecAliqIRRF.setFlex(false);
        FDecAliqIRRF.setRequired(false);
        FDecAliqIRRF.setConstraintExpression("value < 0");
        FDecAliqIRRF.setConstraintMessage("Valor negativo n\u00E3o permitido");
        FDecAliqIRRF.setConstraintCheckWhen("cwImmediate");
        FDecAliqIRRF.setConstraintCheckType("ctExpression");
        FDecAliqIRRF.setConstraintFocusOnError(false);
        FDecAliqIRRF.setConstraintEnableUI(true);
        FDecAliqIRRF.setConstraintEnabled(true);
        FDecAliqIRRF.setConstraintFormCheck(true);
        FDecAliqIRRF.setMaxlength(0);
        FDecAliqIRRF.setPrecision(2);
        FDecAliqIRRF.setFormat(",##0.00");
        FDecAliqIRRF.setFontColor("clWindowText");
        FDecAliqIRRF.setFontSize(-13);
        FDecAliqIRRF.setFontName("Tahoma");
        FDecAliqIRRF.setFontStyle("[]");
        FDecAliqIRRF.setAlignment("taRightJustify");
        FHBox3.addChildren(FDecAliqIRRF);
        FDecAliqIRRF.applyProperties();
        addValidatable(FDecAliqIRRF);
    }

    public TFVBox FVBox30 = new TFVBox();

    private void init_FVBox30() {
        FVBox30.setName("FVBox30");
        FVBox30.setLeft(231);
        FVBox30.setTop(0);
        FVBox30.setWidth(29);
        FVBox30.setHeight(33);
        FVBox30.setBorderStyle("stNone");
        FVBox30.setPaddingTop(5);
        FVBox30.setPaddingLeft(0);
        FVBox30.setPaddingRight(0);
        FVBox30.setPaddingBottom(0);
        FVBox30.setMarginTop(0);
        FVBox30.setMarginLeft(0);
        FVBox30.setMarginRight(0);
        FVBox30.setMarginBottom(0);
        FVBox30.setSpacing(1);
        FVBox30.setFlexVflex("ftFalse");
        FVBox30.setFlexHflex("ftFalse");
        FVBox30.setScrollable(false);
        FVBox30.setBoxShadowConfigHorizontalLength(10);
        FVBox30.setBoxShadowConfigVerticalLength(10);
        FVBox30.setBoxShadowConfigBlurRadius(5);
        FVBox30.setBoxShadowConfigSpreadRadius(0);
        FVBox30.setBoxShadowConfigShadowColor("clBlack");
        FVBox30.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox30);
        FVBox30.applyProperties();
    }

    public TFLabel FLabel18 = new TFLabel();

    private void init_FLabel18() {
        FLabel18.setName("FLabel18");
        FLabel18.setLeft(0);
        FLabel18.setTop(0);
        FLabel18.setWidth(11);
        FLabel18.setHeight(13);
        FLabel18.setCaption("%");
        FLabel18.setFontColor("clWindowText");
        FLabel18.setFontSize(-11);
        FLabel18.setFontName("Tahoma");
        FLabel18.setFontStyle("[]");
        FLabel18.setVerticalAlignment("taVerticalCenter");
        FLabel18.setWordBreak(false);
        FVBox30.addChildren(FLabel18);
        FLabel18.applyProperties();
    }

    public TFHBox FHBox18 = new TFHBox();

    private void init_FHBox18() {
        FHBox18.setName("FHBox18");
        FHBox18.setLeft(0);
        FHBox18.setTop(38);
        FHBox18.setWidth(273);
        FHBox18.setHeight(39);
        FHBox18.setBorderStyle("stNone");
        FHBox18.setPaddingTop(0);
        FHBox18.setPaddingLeft(0);
        FHBox18.setPaddingRight(0);
        FHBox18.setPaddingBottom(0);
        FHBox18.setMarginTop(3);
        FHBox18.setMarginLeft(0);
        FHBox18.setMarginRight(0);
        FHBox18.setMarginBottom(0);
        FHBox18.setSpacing(1);
        FHBox18.setFlexVflex("ftMin");
        FHBox18.setFlexHflex("ftMin");
        FHBox18.setScrollable(false);
        FHBox18.setBoxShadowConfigHorizontalLength(10);
        FHBox18.setBoxShadowConfigVerticalLength(10);
        FHBox18.setBoxShadowConfigBlurRadius(5);
        FHBox18.setBoxShadowConfigSpreadRadius(0);
        FHBox18.setBoxShadowConfigShadowColor("clBlack");
        FHBox18.setBoxShadowConfigOpacity(75);
        FHBox18.setVAlign("tvTop");
        FVBox66.addChildren(FHBox18);
        FHBox18.applyProperties();
    }

    public TFVBox FVBox69 = new TFVBox();

    private void init_FVBox69() {
        FVBox69.setName("FVBox69");
        FVBox69.setLeft(0);
        FVBox69.setTop(0);
        FVBox69.setWidth(166);
        FVBox69.setHeight(32);
        FVBox69.setBorderStyle("stNone");
        FVBox69.setPaddingTop(5);
        FVBox69.setPaddingLeft(0);
        FVBox69.setPaddingRight(0);
        FVBox69.setPaddingBottom(0);
        FVBox69.setMarginTop(0);
        FVBox69.setMarginLeft(0);
        FVBox69.setMarginRight(0);
        FVBox69.setMarginBottom(0);
        FVBox69.setSpacing(1);
        FVBox69.setFlexVflex("ftTrue");
        FVBox69.setFlexHflex("ftMin");
        FVBox69.setScrollable(false);
        FVBox69.setBoxShadowConfigHorizontalLength(10);
        FVBox69.setBoxShadowConfigVerticalLength(10);
        FVBox69.setBoxShadowConfigBlurRadius(5);
        FVBox69.setBoxShadowConfigSpreadRadius(0);
        FVBox69.setBoxShadowConfigShadowColor("clBlack");
        FVBox69.setBoxShadowConfigOpacity(75);
        FHBox18.addChildren(FVBox69);
        FVBox69.applyProperties();
    }

    public TFCheckBox ckbRetemInssCad = new TFCheckBox();

    private void init_ckbRetemInssCad() {
        ckbRetemInssCad.setName("ckbRetemInssCad");
        ckbRetemInssCad.setLeft(0);
        ckbRetemInssCad.setTop(0);
        ckbRetemInssCad.setWidth(165);
        ckbRetemInssCad.setHeight(17);
        ckbRetemInssCad.setCaption("Retem INSS com Aliquota de:");
        ckbRetemInssCad.setFontColor("clWindowText");
        ckbRetemInssCad.setFontSize(-11);
        ckbRetemInssCad.setFontName("Tahoma");
        ckbRetemInssCad.setFontStyle("[]");
        ckbRetemInssCad.setTable(tbServicos);
        ckbRetemInssCad.setFieldName("RETEM_INSS");
        ckbRetemInssCad.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            ckbRetemInssCadCheck(event);
            processarFlow("FrmServicos", "ckbRetemInssCad", "OnCheck");
        });
        ckbRetemInssCad.setVerticalAlignment("taAlignTop");
        FVBox69.addChildren(ckbRetemInssCad);
        ckbRetemInssCad.applyProperties();
    }

    public TFDecimal FDecAliqInss = new TFDecimal();

    private void init_FDecAliqInss() {
        FDecAliqInss.setName("FDecAliqInss");
        FDecAliqInss.setLeft(166);
        FDecAliqInss.setTop(0);
        FDecAliqInss.setWidth(65);
        FDecAliqInss.setHeight(24);
        FDecAliqInss.setTable(tbServicos);
        FDecAliqInss.setFieldName("ALIQ_INSS");
        FDecAliqInss.setFlex(false);
        FDecAliqInss.setRequired(false);
        FDecAliqInss.setConstraintExpression("value < 0");
        FDecAliqInss.setConstraintMessage("Valor negativo n\u00E3o permitido");
        FDecAliqInss.setConstraintCheckWhen("cwImmediate");
        FDecAliqInss.setConstraintCheckType("ctExpression");
        FDecAliqInss.setConstraintFocusOnError(false);
        FDecAliqInss.setConstraintEnableUI(true);
        FDecAliqInss.setConstraintEnabled(true);
        FDecAliqInss.setConstraintFormCheck(true);
        FDecAliqInss.setMaxlength(0);
        FDecAliqInss.setPrecision(2);
        FDecAliqInss.setFormat(",##0.00");
        FDecAliqInss.setFontColor("clWindowText");
        FDecAliqInss.setFontSize(-13);
        FDecAliqInss.setFontName("Tahoma");
        FDecAliqInss.setFontStyle("[]");
        FDecAliqInss.setAlignment("taRightJustify");
        FHBox18.addChildren(FDecAliqInss);
        FDecAliqInss.applyProperties();
        addValidatable(FDecAliqInss);
    }

    public TFVBox FVBox31 = new TFVBox();

    private void init_FVBox31() {
        FVBox31.setName("FVBox31");
        FVBox31.setLeft(231);
        FVBox31.setTop(0);
        FVBox31.setWidth(29);
        FVBox31.setHeight(30);
        FVBox31.setBorderStyle("stNone");
        FVBox31.setPaddingTop(5);
        FVBox31.setPaddingLeft(0);
        FVBox31.setPaddingRight(0);
        FVBox31.setPaddingBottom(0);
        FVBox31.setMarginTop(0);
        FVBox31.setMarginLeft(0);
        FVBox31.setMarginRight(0);
        FVBox31.setMarginBottom(0);
        FVBox31.setSpacing(1);
        FVBox31.setFlexVflex("ftFalse");
        FVBox31.setFlexHflex("ftFalse");
        FVBox31.setScrollable(false);
        FVBox31.setBoxShadowConfigHorizontalLength(10);
        FVBox31.setBoxShadowConfigVerticalLength(10);
        FVBox31.setBoxShadowConfigBlurRadius(5);
        FVBox31.setBoxShadowConfigSpreadRadius(0);
        FVBox31.setBoxShadowConfigShadowColor("clBlack");
        FVBox31.setBoxShadowConfigOpacity(75);
        FHBox18.addChildren(FVBox31);
        FVBox31.applyProperties();
    }

    public TFLabel FLabel19 = new TFLabel();

    private void init_FLabel19() {
        FLabel19.setName("FLabel19");
        FLabel19.setLeft(0);
        FLabel19.setTop(0);
        FLabel19.setWidth(11);
        FLabel19.setHeight(13);
        FLabel19.setCaption("%");
        FLabel19.setFontColor("clWindowText");
        FLabel19.setFontSize(-11);
        FLabel19.setFontName("Tahoma");
        FLabel19.setFontStyle("[]");
        FLabel19.setVerticalAlignment("taVerticalCenter");
        FLabel19.setWordBreak(false);
        FVBox31.addChildren(FLabel19);
        FLabel19.applyProperties();
    }

    public TFVBox FVBox67 = new TFVBox();

    private void init_FVBox67() {
        FVBox67.setName("FVBox67");
        FVBox67.setLeft(276);
        FVBox67.setTop(0);
        FVBox67.setWidth(283);
        FVBox67.setHeight(88);
        FVBox67.setBorderStyle("stNone");
        FVBox67.setPaddingTop(0);
        FVBox67.setPaddingLeft(0);
        FVBox67.setPaddingRight(0);
        FVBox67.setPaddingBottom(0);
        FVBox67.setMarginTop(0);
        FVBox67.setMarginLeft(0);
        FVBox67.setMarginRight(0);
        FVBox67.setMarginBottom(0);
        FVBox67.setSpacing(1);
        FVBox67.setFlexVflex("ftTrue");
        FVBox67.setFlexHflex("ftTrue");
        FVBox67.setScrollable(false);
        FVBox67.setBoxShadowConfigHorizontalLength(10);
        FVBox67.setBoxShadowConfigVerticalLength(10);
        FVBox67.setBoxShadowConfigBlurRadius(5);
        FVBox67.setBoxShadowConfigSpreadRadius(0);
        FVBox67.setBoxShadowConfigShadowColor("clBlack");
        FVBox67.setBoxShadowConfigOpacity(75);
        FHBox35.addChildren(FVBox67);
        FVBox67.applyProperties();
    }

    public TFVBox FVBox65 = new TFVBox();

    private void init_FVBox65() {
        FVBox65.setName("FVBox65");
        FVBox65.setLeft(0);
        FVBox65.setTop(0);
        FVBox65.setWidth(105);
        FVBox65.setHeight(29);
        FVBox65.setBorderStyle("stNone");
        FVBox65.setPaddingTop(0);
        FVBox65.setPaddingLeft(0);
        FVBox65.setPaddingRight(0);
        FVBox65.setPaddingBottom(0);
        FVBox65.setMarginTop(0);
        FVBox65.setMarginLeft(5);
        FVBox65.setMarginRight(0);
        FVBox65.setMarginBottom(0);
        FVBox65.setSpacing(1);
        FVBox65.setFlexVflex("ftMin");
        FVBox65.setFlexHflex("ftMin");
        FVBox65.setScrollable(false);
        FVBox65.setBoxShadowConfigHorizontalLength(10);
        FVBox65.setBoxShadowConfigVerticalLength(10);
        FVBox65.setBoxShadowConfigBlurRadius(5);
        FVBox65.setBoxShadowConfigSpreadRadius(0);
        FVBox65.setBoxShadowConfigShadowColor("clBlack");
        FVBox65.setBoxShadowConfigOpacity(75);
        FVBox67.addChildren(FVBox65);
        FVBox65.applyProperties();
    }

    public TFCheckBox cknNRetemPCC = new TFCheckBox();

    private void init_cknNRetemPCC() {
        cknNRetemPCC.setName("cknNRetemPCC");
        cknNRetemPCC.setLeft(0);
        cknNRetemPCC.setTop(0);
        cknNRetemPCC.setWidth(97);
        cknNRetemPCC.setHeight(17);
        cknNRetemPCC.setCaption("N\u00E3o Retem PCC");
        cknNRetemPCC.setFontColor("clWindowText");
        cknNRetemPCC.setFontSize(-11);
        cknNRetemPCC.setFontName("Tahoma");
        cknNRetemPCC.setFontStyle("[]");
        cknNRetemPCC.setTable(tbServicos);
        cknNRetemPCC.setFieldName("NAO_RETEM_PCC");
        cknNRetemPCC.setVerticalAlignment("taAlignTop");
        FVBox65.addChildren(cknNRetemPCC);
        cknNRetemPCC.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(30);
        FHBox7.setWidth(223);
        FHBox7.setHeight(53);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftMin");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FVBox67.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFVBox FVBox28 = new TFVBox();

    private void init_FVBox28() {
        FVBox28.setName("FVBox28");
        FVBox28.setLeft(0);
        FVBox28.setTop(0);
        FVBox28.setWidth(217);
        FVBox28.setHeight(46);
        FVBox28.setBorderStyle("stNone");
        FVBox28.setPaddingTop(0);
        FVBox28.setPaddingLeft(0);
        FVBox28.setPaddingRight(0);
        FVBox28.setPaddingBottom(0);
        FVBox28.setMarginTop(0);
        FVBox28.setMarginLeft(0);
        FVBox28.setMarginRight(0);
        FVBox28.setMarginBottom(0);
        FVBox28.setSpacing(1);
        FVBox28.setFlexVflex("ftMin");
        FVBox28.setFlexHflex("ftTrue");
        FVBox28.setScrollable(false);
        FVBox28.setBoxShadowConfigHorizontalLength(10);
        FVBox28.setBoxShadowConfigVerticalLength(10);
        FVBox28.setBoxShadowConfigBlurRadius(5);
        FVBox28.setBoxShadowConfigSpreadRadius(0);
        FVBox28.setBoxShadowConfigShadowColor("clBlack");
        FVBox28.setBoxShadowConfigOpacity(75);
        FHBox7.addChildren(FVBox28);
        FVBox28.applyProperties();
    }

    public TFLabel FLabel40 = new TFLabel();

    private void init_FLabel40() {
        FLabel40.setName("FLabel40");
        FLabel40.setLeft(0);
        FLabel40.setTop(0);
        FLabel40.setWidth(112);
        FLabel40.setHeight(13);
        FLabel40.setCaption("Classifica\u00E7\u00E3o EFD-Reinf");
        FLabel40.setFontColor("clWindowText");
        FLabel40.setFontSize(-11);
        FLabel40.setFontName("Tahoma");
        FLabel40.setFontStyle("[]");
        FLabel40.setVerticalAlignment("taVerticalCenter");
        FLabel40.setWordBreak(false);
        FVBox28.addChildren(FLabel40);
        FLabel40.applyProperties();
    }

    public TFCombo cbClassReinfCad = new TFCombo();

    private void init_cbClassReinfCad() {
        cbClassReinfCad.setName("cbClassReinfCad");
        cbClassReinfCad.setLeft(0);
        cbClassReinfCad.setTop(14);
        cbClassReinfCad.setWidth(211);
        cbClassReinfCad.setHeight(21);
        cbClassReinfCad.setTable(tbServicos);
        cbClassReinfCad.setLookupTable(tbReinfClassifEfd);
        cbClassReinfCad.setFieldName("CLASSIF_EFD_REINF");
        cbClassReinfCad.setLookupKey("COD_CLASSIF");
        cbClassReinfCad.setLookupDesc("DESCRICAO");
        cbClassReinfCad.setFlex(true);
        cbClassReinfCad.setReadOnly(true);
        cbClassReinfCad.setRequired(false);
        cbClassReinfCad.setPrompt("Classifica\u00E7\u00E3o EFD-Reinf");
        cbClassReinfCad.setConstraintCheckWhen("cwImmediate");
        cbClassReinfCad.setConstraintCheckType("ctExpression");
        cbClassReinfCad.setConstraintFocusOnError(false);
        cbClassReinfCad.setConstraintEnableUI(true);
        cbClassReinfCad.setConstraintEnabled(false);
        cbClassReinfCad.setConstraintFormCheck(true);
        cbClassReinfCad.setClearOnDelKey(true);
        cbClassReinfCad.setUseClearButton(false);
        cbClassReinfCad.setHideClearButtonOnNullValue(true);
        cbClassReinfCad.setEnabled(false);
        FVBox28.addChildren(cbClassReinfCad);
        cbClassReinfCad.applyProperties();
        addValidatable(cbClassReinfCad);
    }

    public TFHBox FHBox21 = new TFHBox();

    private void init_FHBox21() {
        FHBox21.setName("FHBox21");
        FHBox21.setLeft(0);
        FHBox21.setTop(125);
        FHBox21.setWidth(563);
        FHBox21.setHeight(401);
        FHBox21.setBorderStyle("stNone");
        FHBox21.setPaddingTop(0);
        FHBox21.setPaddingLeft(0);
        FHBox21.setPaddingRight(0);
        FHBox21.setPaddingBottom(0);
        FHBox21.setMarginTop(0);
        FHBox21.setMarginLeft(0);
        FHBox21.setMarginRight(0);
        FHBox21.setMarginBottom(0);
        FHBox21.setSpacing(20);
        FHBox21.setFlexVflex("ftTrue");
        FHBox21.setFlexHflex("ftTrue");
        FHBox21.setScrollable(true);
        FHBox21.setBoxShadowConfigHorizontalLength(10);
        FHBox21.setBoxShadowConfigVerticalLength(10);
        FHBox21.setBoxShadowConfigBlurRadius(5);
        FHBox21.setBoxShadowConfigSpreadRadius(0);
        FHBox21.setBoxShadowConfigShadowColor("clBlack");
        FHBox21.setBoxShadowConfigOpacity(75);
        FHBox21.setVAlign("tvTop");
        vBoxImposto.addChildren(FHBox21);
        FHBox21.applyProperties();
    }

    public TFHBox hBoxGridEmpresaTabImposto = new TFHBox();

    private void init_hBoxGridEmpresaTabImposto() {
        hBoxGridEmpresaTabImposto.setName("hBoxGridEmpresaTabImposto");
        hBoxGridEmpresaTabImposto.setLeft(0);
        hBoxGridEmpresaTabImposto.setTop(0);
        hBoxGridEmpresaTabImposto.setWidth(185);
        hBoxGridEmpresaTabImposto.setHeight(393);
        hBoxGridEmpresaTabImposto.setAlign("alLeft");
        hBoxGridEmpresaTabImposto.setBorderStyle("stNone");
        hBoxGridEmpresaTabImposto.setPaddingTop(0);
        hBoxGridEmpresaTabImposto.setPaddingLeft(0);
        hBoxGridEmpresaTabImposto.setPaddingRight(0);
        hBoxGridEmpresaTabImposto.setPaddingBottom(0);
        hBoxGridEmpresaTabImposto.setMarginTop(0);
        hBoxGridEmpresaTabImposto.setMarginLeft(0);
        hBoxGridEmpresaTabImposto.setMarginRight(0);
        hBoxGridEmpresaTabImposto.setMarginBottom(0);
        hBoxGridEmpresaTabImposto.setSpacing(1);
        hBoxGridEmpresaTabImposto.setFlexVflex("ftTrue");
        hBoxGridEmpresaTabImposto.setFlexHflex("ftTrue");
        hBoxGridEmpresaTabImposto.setScrollable(false);
        hBoxGridEmpresaTabImposto.setBoxShadowConfigHorizontalLength(10);
        hBoxGridEmpresaTabImposto.setBoxShadowConfigVerticalLength(10);
        hBoxGridEmpresaTabImposto.setBoxShadowConfigBlurRadius(5);
        hBoxGridEmpresaTabImposto.setBoxShadowConfigSpreadRadius(0);
        hBoxGridEmpresaTabImposto.setBoxShadowConfigShadowColor("clBlack");
        hBoxGridEmpresaTabImposto.setBoxShadowConfigOpacity(75);
        hBoxGridEmpresaTabImposto.setVAlign("tvTop");
        FHBox21.addChildren(hBoxGridEmpresaTabImposto);
        hBoxGridEmpresaTabImposto.applyProperties();
    }

    public TFGrid gridEmpresasImposto = new TFGrid();

    private void init_gridEmpresasImposto() {
        gridEmpresasImposto.setName("gridEmpresasImposto");
        gridEmpresasImposto.setLeft(0);
        gridEmpresasImposto.setTop(0);
        gridEmpresasImposto.setWidth(288);
        gridEmpresasImposto.setHeight(366);
        gridEmpresasImposto.setTable(tbEmpresas);
        gridEmpresasImposto.setFlexVflex("ftTrue");
        gridEmpresasImposto.setFlexHflex("ftTrue");
        gridEmpresasImposto.setPagingEnabled(false);
        gridEmpresasImposto.setFrozenColumns(0);
        gridEmpresasImposto.setShowFooter(false);
        gridEmpresasImposto.setShowHeader(true);
        gridEmpresasImposto.setMultiSelection(false);
        gridEmpresasImposto.setGroupingEnabled(false);
        gridEmpresasImposto.setGroupingExpanded(false);
        gridEmpresasImposto.setGroupingShowFooter(false);
        gridEmpresasImposto.setCrosstabEnabled(false);
        gridEmpresasImposto.setCrosstabGroupType("cgtConcat");
        gridEmpresasImposto.setEditionEnabled(false);
        gridEmpresasImposto.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_EMPRESA");
        item0.setTitleCaption("CE");
        item0.setWidth(47);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(true);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridEmpresasImposto.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("NOME");
        item1.setTitleCaption("Nome");
        item1.setWidth(100);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(true);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridEmpresasImposto.getColumns().add(item1);
        hBoxGridEmpresaTabImposto.addChildren(gridEmpresasImposto);
        gridEmpresasImposto.applyProperties();
    }

    public TFHBox FHBox40 = new TFHBox();

    private void init_FHBox40() {
        FHBox40.setName("FHBox40");
        FHBox40.setLeft(185);
        FHBox40.setTop(0);
        FHBox40.setWidth(101);
        FHBox40.setHeight(392);
        FHBox40.setBorderStyle("stNone");
        FHBox40.setPaddingTop(0);
        FHBox40.setPaddingLeft(0);
        FHBox40.setPaddingRight(0);
        FHBox40.setPaddingBottom(0);
        FHBox40.setMarginTop(0);
        FHBox40.setMarginLeft(0);
        FHBox40.setMarginRight(0);
        FHBox40.setMarginBottom(0);
        FHBox40.setSpacing(0);
        FHBox40.setFlexVflex("ftTrue");
        FHBox40.setFlexHflex("ftMin");
        FHBox40.setScrollable(false);
        FHBox40.setBoxShadowConfigHorizontalLength(10);
        FHBox40.setBoxShadowConfigVerticalLength(10);
        FHBox40.setBoxShadowConfigBlurRadius(5);
        FHBox40.setBoxShadowConfigSpreadRadius(0);
        FHBox40.setBoxShadowConfigShadowColor("clBlack");
        FHBox40.setBoxShadowConfigOpacity(75);
        FHBox40.setVAlign("tvTop");
        FHBox21.addChildren(FHBox40);
        FHBox40.applyProperties();
    }

    public TFVBox FVBox78 = new TFVBox();

    private void init_FVBox78() {
        FVBox78.setName("FVBox78");
        FVBox78.setLeft(0);
        FVBox78.setTop(0);
        FVBox78.setWidth(96);
        FVBox78.setHeight(386);
        FVBox78.setBorderStyle("stNone");
        FVBox78.setPaddingTop(0);
        FVBox78.setPaddingLeft(0);
        FVBox78.setPaddingRight(0);
        FVBox78.setPaddingBottom(0);
        FVBox78.setMarginTop(0);
        FVBox78.setMarginLeft(0);
        FVBox78.setMarginRight(0);
        FVBox78.setMarginBottom(0);
        FVBox78.setSpacing(0);
        FVBox78.setFlexVflex("ftTrue");
        FVBox78.setFlexHflex("ftMin");
        FVBox78.setScrollable(false);
        FVBox78.setBoxShadowConfigHorizontalLength(10);
        FVBox78.setBoxShadowConfigVerticalLength(10);
        FVBox78.setBoxShadowConfigBlurRadius(5);
        FVBox78.setBoxShadowConfigSpreadRadius(0);
        FVBox78.setBoxShadowConfigShadowColor("clBlack");
        FVBox78.setBoxShadowConfigOpacity(75);
        FHBox40.addChildren(FVBox78);
        FVBox78.applyProperties();
    }

    public TFVBox FVBox33 = new TFVBox();

    private void init_FVBox33() {
        FVBox33.setName("FVBox33");
        FVBox33.setLeft(0);
        FVBox33.setTop(0);
        FVBox33.setWidth(91);
        FVBox33.setHeight(188);
        FVBox33.setBorderStyle("stNone");
        FVBox33.setPaddingTop(0);
        FVBox33.setPaddingLeft(0);
        FVBox33.setPaddingRight(0);
        FVBox33.setPaddingBottom(0);
        FVBox33.setMarginTop(20);
        FVBox33.setMarginLeft(0);
        FVBox33.setMarginRight(0);
        FVBox33.setMarginBottom(0);
        FVBox33.setSpacing(1);
        FVBox33.setFlexVflex("ftMin");
        FVBox33.setFlexHflex("ftMin");
        FVBox33.setScrollable(false);
        FVBox33.setBoxShadowConfigHorizontalLength(10);
        FVBox33.setBoxShadowConfigVerticalLength(10);
        FVBox33.setBoxShadowConfigBlurRadius(5);
        FVBox33.setBoxShadowConfigSpreadRadius(0);
        FVBox33.setBoxShadowConfigShadowColor("clBlack");
        FVBox33.setBoxShadowConfigOpacity(75);
        FVBox78.addChildren(FVBox33);
        FVBox33.applyProperties();
    }

    public TFVBox FVBox71 = new TFVBox();

    private void init_FVBox71() {
        FVBox71.setName("FVBox71");
        FVBox71.setLeft(0);
        FVBox71.setTop(0);
        FVBox71.setWidth(87);
        FVBox71.setHeight(121);
        FVBox71.setBorderStyle("stNone");
        FVBox71.setPaddingTop(0);
        FVBox71.setPaddingLeft(0);
        FVBox71.setPaddingRight(0);
        FVBox71.setPaddingBottom(0);
        FVBox71.setMarginTop(0);
        FVBox71.setMarginLeft(20);
        FVBox71.setMarginRight(0);
        FVBox71.setMarginBottom(0);
        FVBox71.setSpacing(3);
        FVBox71.setFlexVflex("ftFalse");
        FVBox71.setFlexHflex("ftMin");
        FVBox71.setScrollable(false);
        FVBox71.setBoxShadowConfigHorizontalLength(10);
        FVBox71.setBoxShadowConfigVerticalLength(10);
        FVBox71.setBoxShadowConfigBlurRadius(5);
        FVBox71.setBoxShadowConfigSpreadRadius(0);
        FVBox71.setBoxShadowConfigShadowColor("clBlack");
        FVBox71.setBoxShadowConfigOpacity(75);
        FVBox33.addChildren(FVBox71);
        FVBox71.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(0);
        FVBox5.setTop(0);
        FVBox5.setWidth(41);
        FVBox5.setHeight(17);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftTrue");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FVBox71.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFButton btnInsCodPref = new TFButton();

    private void init_btnInsCodPref() {
        btnInsCodPref.setName("btnInsCodPref");
        btnInsCodPref.setLeft(0);
        btnInsCodPref.setTop(18);
        btnInsCodPref.setWidth(40);
        btnInsCodPref.setHeight(40);
        btnInsCodPref.setCaption(">");
        btnInsCodPref.setFontColor("clMenuText");
        btnInsCodPref.setFontSize(-16);
        btnInsCodPref.setFontName("Tahoma");
        btnInsCodPref.setFontStyle("[]");
        btnInsCodPref.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnInsCodPrefClick(event);
            processarFlow("FrmServicos", "btnInsCodPref", "OnClick");
        });
        btnInsCodPref.setImageId(0);
        btnInsCodPref.setColor("clBtnFace");
        btnInsCodPref.setAccess(false);
        btnInsCodPref.setIconReverseDirection(false);
        FVBox71.addChildren(btnInsCodPref);
        btnInsCodPref.applyProperties();
    }

    public TFButton btnExcCodPref = new TFButton();

    private void init_btnExcCodPref() {
        btnExcCodPref.setName("btnExcCodPref");
        btnExcCodPref.setLeft(0);
        btnExcCodPref.setTop(59);
        btnExcCodPref.setWidth(40);
        btnExcCodPref.setHeight(40);
        btnExcCodPref.setCaption("<");
        btnExcCodPref.setFontColor("clMenuText");
        btnExcCodPref.setFontSize(-16);
        btnExcCodPref.setFontName("Tahoma");
        btnExcCodPref.setFontStyle("[]");
        btnExcCodPref.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcCodPrefClick(event);
            processarFlow("FrmServicos", "btnExcCodPref", "OnClick");
        });
        btnExcCodPref.setImageId(0);
        btnExcCodPref.setColor("clBtnFace");
        btnExcCodPref.setAccess(false);
        btnExcCodPref.setIconReverseDirection(false);
        FVBox71.addChildren(btnExcCodPref);
        btnExcCodPref.applyProperties();
    }

    public TFVBox FVBox9 = new TFVBox();

    private void init_FVBox9() {
        FVBox9.setName("FVBox9");
        FVBox9.setLeft(0);
        FVBox9.setTop(100);
        FVBox9.setWidth(41);
        FVBox9.setHeight(17);
        FVBox9.setBorderStyle("stNone");
        FVBox9.setPaddingTop(0);
        FVBox9.setPaddingLeft(0);
        FVBox9.setPaddingRight(0);
        FVBox9.setPaddingBottom(0);
        FVBox9.setMarginTop(0);
        FVBox9.setMarginLeft(0);
        FVBox9.setMarginRight(0);
        FVBox9.setMarginBottom(0);
        FVBox9.setSpacing(1);
        FVBox9.setFlexVflex("ftTrue");
        FVBox9.setFlexHflex("ftFalse");
        FVBox9.setScrollable(false);
        FVBox9.setBoxShadowConfigHorizontalLength(10);
        FVBox9.setBoxShadowConfigVerticalLength(10);
        FVBox9.setBoxShadowConfigBlurRadius(5);
        FVBox9.setBoxShadowConfigSpreadRadius(0);
        FVBox9.setBoxShadowConfigShadowColor("clBlack");
        FVBox9.setBoxShadowConfigOpacity(75);
        FVBox71.addChildren(FVBox9);
        FVBox9.applyProperties();
    }

    public TFVBox FVBox34 = new TFVBox();

    private void init_FVBox34() {
        FVBox34.setName("FVBox34");
        FVBox34.setLeft(0);
        FVBox34.setTop(122);
        FVBox34.setWidth(87);
        FVBox34.setHeight(53);
        FVBox34.setBorderStyle("stNone");
        FVBox34.setPaddingTop(0);
        FVBox34.setPaddingLeft(0);
        FVBox34.setPaddingRight(0);
        FVBox34.setPaddingBottom(0);
        FVBox34.setMarginTop(0);
        FVBox34.setMarginLeft(0);
        FVBox34.setMarginRight(0);
        FVBox34.setMarginBottom(0);
        FVBox34.setSpacing(1);
        FVBox34.setFlexVflex("ftMin");
        FVBox34.setFlexHflex("ftMin");
        FVBox34.setScrollable(false);
        FVBox34.setBoxShadowConfigHorizontalLength(10);
        FVBox34.setBoxShadowConfigVerticalLength(10);
        FVBox34.setBoxShadowConfigBlurRadius(5);
        FVBox34.setBoxShadowConfigSpreadRadius(0);
        FVBox34.setBoxShadowConfigShadowColor("clBlack");
        FVBox34.setBoxShadowConfigOpacity(75);
        FVBox33.addChildren(FVBox34);
        FVBox34.applyProperties();
    }

    public TFVBox FVBox36 = new TFVBox();

    private void init_FVBox36() {
        FVBox36.setName("FVBox36");
        FVBox36.setLeft(0);
        FVBox36.setTop(0);
        FVBox36.setWidth(82);
        FVBox36.setHeight(48);
        FVBox36.setBorderStyle("stNone");
        FVBox36.setPaddingTop(0);
        FVBox36.setPaddingLeft(0);
        FVBox36.setPaddingRight(0);
        FVBox36.setPaddingBottom(0);
        FVBox36.setMarginTop(0);
        FVBox36.setMarginLeft(0);
        FVBox36.setMarginRight(0);
        FVBox36.setMarginBottom(0);
        FVBox36.setSpacing(0);
        FVBox36.setFlexVflex("ftTrue");
        FVBox36.setFlexHflex("ftFalse");
        FVBox36.setScrollable(false);
        FVBox36.setBoxShadowConfigHorizontalLength(10);
        FVBox36.setBoxShadowConfigVerticalLength(10);
        FVBox36.setBoxShadowConfigBlurRadius(5);
        FVBox36.setBoxShadowConfigSpreadRadius(0);
        FVBox36.setBoxShadowConfigShadowColor("clBlack");
        FVBox36.setBoxShadowConfigOpacity(75);
        FVBox34.addChildren(FVBox36);
        FVBox36.applyProperties();
    }

    public TFLabel FLabel20 = new TFLabel();

    private void init_FLabel20() {
        FLabel20.setName("FLabel20");
        FLabel20.setLeft(0);
        FLabel20.setTop(0);
        FLabel20.setWidth(74);
        FLabel20.setHeight(13);
        FLabel20.setCaption("Cod. Prefeitura");
        FLabel20.setFontColor("clWindowText");
        FLabel20.setFontSize(-11);
        FLabel20.setFontName("Tahoma");
        FLabel20.setFontStyle("[]");
        FLabel20.setVerticalAlignment("taVerticalCenter");
        FLabel20.setWordBreak(false);
        FVBox36.addChildren(FLabel20);
        FLabel20.applyProperties();
    }

    public TFString FStrCodPref = new TFString();

    private void init_FStrCodPref() {
        FStrCodPref.setName("FStrCodPref");
        FStrCodPref.setLeft(0);
        FStrCodPref.setTop(14);
        FStrCodPref.setWidth(76);
        FStrCodPref.setHeight(24);
        FStrCodPref.setFlex(true);
        FStrCodPref.setRequired(true);
        FStrCodPref.setConstraintExpression("value < 0");
        FStrCodPref.setConstraintMessage("Valor negativo n\u00E3o permitido");
        FStrCodPref.setConstraintCheckWhen("cwImmediate");
        FStrCodPref.setConstraintCheckType("ctExpression");
        FStrCodPref.setConstraintFocusOnError(false);
        FStrCodPref.setConstraintEnableUI(true);
        FStrCodPref.setConstraintEnabled(true);
        FStrCodPref.setConstraintFormCheck(true);
        FStrCodPref.setCharCase("ccNormal");
        FStrCodPref.setPwd(false);
        FStrCodPref.setMaxlength(20);
        FStrCodPref.setFontColor("clWindowText");
        FStrCodPref.setFontSize(-13);
        FStrCodPref.setFontName("Tahoma");
        FStrCodPref.setFontStyle("[]");
        FStrCodPref.setAlignment("taRightJustify");
        FStrCodPref.setSaveLiteralCharacter(false);
        FStrCodPref.applyProperties();
        FVBox36.addChildren(FStrCodPref);
        addValidatable(FStrCodPref);
    }

    public TFVBox FVBox73 = new TFVBox();

    private void init_FVBox73() {
        FVBox73.setName("FVBox73");
        FVBox73.setLeft(0);
        FVBox73.setTop(189);
        FVBox73.setWidth(91);
        FVBox73.setHeight(191);
        FVBox73.setBorderStyle("stNone");
        FVBox73.setPaddingTop(0);
        FVBox73.setPaddingLeft(0);
        FVBox73.setPaddingRight(0);
        FVBox73.setPaddingBottom(0);
        FVBox73.setMarginTop(20);
        FVBox73.setMarginLeft(0);
        FVBox73.setMarginRight(0);
        FVBox73.setMarginBottom(0);
        FVBox73.setSpacing(0);
        FVBox73.setFlexVflex("ftMin");
        FVBox73.setFlexHflex("ftMin");
        FVBox73.setScrollable(false);
        FVBox73.setBoxShadowConfigHorizontalLength(10);
        FVBox73.setBoxShadowConfigVerticalLength(10);
        FVBox73.setBoxShadowConfigBlurRadius(5);
        FVBox73.setBoxShadowConfigSpreadRadius(0);
        FVBox73.setBoxShadowConfigShadowColor("clBlack");
        FVBox73.setBoxShadowConfigOpacity(75);
        FVBox78.addChildren(FVBox73);
        FVBox73.applyProperties();
    }

    public TFVBox FVBox38 = new TFVBox();

    private void init_FVBox38() {
        FVBox38.setName("FVBox38");
        FVBox38.setLeft(0);
        FVBox38.setTop(0);
        FVBox38.setWidth(46);
        FVBox38.setHeight(107);
        FVBox38.setBorderStyle("stNone");
        FVBox38.setPaddingTop(0);
        FVBox38.setPaddingLeft(0);
        FVBox38.setPaddingRight(0);
        FVBox38.setPaddingBottom(0);
        FVBox38.setMarginTop(0);
        FVBox38.setMarginLeft(25);
        FVBox38.setMarginRight(0);
        FVBox38.setMarginBottom(0);
        FVBox38.setSpacing(3);
        FVBox38.setFlexVflex("ftFalse");
        FVBox38.setFlexHflex("ftMin");
        FVBox38.setScrollable(false);
        FVBox38.setBoxShadowConfigHorizontalLength(10);
        FVBox38.setBoxShadowConfigVerticalLength(10);
        FVBox38.setBoxShadowConfigBlurRadius(5);
        FVBox38.setBoxShadowConfigSpreadRadius(0);
        FVBox38.setBoxShadowConfigShadowColor("clBlack");
        FVBox38.setBoxShadowConfigOpacity(75);
        FVBox73.addChildren(FVBox38);
        FVBox38.applyProperties();
    }

    public TFVBox FVBox26 = new TFVBox();

    private void init_FVBox26() {
        FVBox26.setName("FVBox26");
        FVBox26.setLeft(0);
        FVBox26.setTop(0);
        FVBox26.setWidth(41);
        FVBox26.setHeight(5);
        FVBox26.setBorderStyle("stNone");
        FVBox26.setPaddingTop(0);
        FVBox26.setPaddingLeft(0);
        FVBox26.setPaddingRight(0);
        FVBox26.setPaddingBottom(0);
        FVBox26.setMarginTop(0);
        FVBox26.setMarginLeft(0);
        FVBox26.setMarginRight(0);
        FVBox26.setMarginBottom(0);
        FVBox26.setSpacing(1);
        FVBox26.setFlexVflex("ftTrue");
        FVBox26.setFlexHflex("ftFalse");
        FVBox26.setScrollable(false);
        FVBox26.setBoxShadowConfigHorizontalLength(10);
        FVBox26.setBoxShadowConfigVerticalLength(10);
        FVBox26.setBoxShadowConfigBlurRadius(5);
        FVBox26.setBoxShadowConfigSpreadRadius(0);
        FVBox26.setBoxShadowConfigShadowColor("clBlack");
        FVBox26.setBoxShadowConfigOpacity(75);
        FVBox38.addChildren(FVBox26);
        FVBox26.applyProperties();
    }

    public TFButton btnInsRedBase = new TFButton();

    private void init_btnInsRedBase() {
        btnInsRedBase.setName("btnInsRedBase");
        btnInsRedBase.setLeft(0);
        btnInsRedBase.setTop(6);
        btnInsRedBase.setWidth(40);
        btnInsRedBase.setHeight(40);
        btnInsRedBase.setCaption(">");
        btnInsRedBase.setFontColor("clMenuText");
        btnInsRedBase.setFontSize(-16);
        btnInsRedBase.setFontName("Tahoma");
        btnInsRedBase.setFontStyle("[]");
        btnInsRedBase.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnInsRedBaseClick(event);
            processarFlow("FrmServicos", "btnInsRedBase", "OnClick");
        });
        btnInsRedBase.setImageId(0);
        btnInsRedBase.setColor("clBtnFace");
        btnInsRedBase.setAccess(false);
        btnInsRedBase.setIconReverseDirection(false);
        FVBox38.addChildren(btnInsRedBase);
        btnInsRedBase.applyProperties();
    }

    public TFButton btnExcRedBase = new TFButton();

    private void init_btnExcRedBase() {
        btnExcRedBase.setName("btnExcRedBase");
        btnExcRedBase.setLeft(0);
        btnExcRedBase.setTop(47);
        btnExcRedBase.setWidth(40);
        btnExcRedBase.setHeight(40);
        btnExcRedBase.setCaption("<");
        btnExcRedBase.setFontColor("clMenuText");
        btnExcRedBase.setFontSize(-16);
        btnExcRedBase.setFontName("Tahoma");
        btnExcRedBase.setFontStyle("[]");
        btnExcRedBase.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcRedBaseClick(event);
            processarFlow("FrmServicos", "btnExcRedBase", "OnClick");
        });
        btnExcRedBase.setImageId(0);
        btnExcRedBase.setColor("clBtnFace");
        btnExcRedBase.setAccess(false);
        btnExcRedBase.setIconReverseDirection(false);
        FVBox38.addChildren(btnExcRedBase);
        btnExcRedBase.applyProperties();
    }

    public TFVBox FVBox77 = new TFVBox();

    private void init_FVBox77() {
        FVBox77.setName("FVBox77");
        FVBox77.setLeft(0);
        FVBox77.setTop(88);
        FVBox77.setWidth(41);
        FVBox77.setHeight(5);
        FVBox77.setBorderStyle("stNone");
        FVBox77.setPaddingTop(0);
        FVBox77.setPaddingLeft(0);
        FVBox77.setPaddingRight(0);
        FVBox77.setPaddingBottom(0);
        FVBox77.setMarginTop(0);
        FVBox77.setMarginLeft(0);
        FVBox77.setMarginRight(0);
        FVBox77.setMarginBottom(0);
        FVBox77.setSpacing(1);
        FVBox77.setFlexVflex("ftTrue");
        FVBox77.setFlexHflex("ftFalse");
        FVBox77.setScrollable(false);
        FVBox77.setBoxShadowConfigHorizontalLength(10);
        FVBox77.setBoxShadowConfigVerticalLength(10);
        FVBox77.setBoxShadowConfigBlurRadius(5);
        FVBox77.setBoxShadowConfigSpreadRadius(0);
        FVBox77.setBoxShadowConfigShadowColor("clBlack");
        FVBox77.setBoxShadowConfigOpacity(75);
        FVBox38.addChildren(FVBox77);
        FVBox77.applyProperties();
    }

    public TFVBox FVBox39 = new TFVBox();

    private void init_FVBox39() {
        FVBox39.setName("FVBox39");
        FVBox39.setLeft(0);
        FVBox39.setTop(108);
        FVBox39.setWidth(83);
        FVBox39.setHeight(61);
        FVBox39.setBorderStyle("stNone");
        FVBox39.setPaddingTop(0);
        FVBox39.setPaddingLeft(0);
        FVBox39.setPaddingRight(0);
        FVBox39.setPaddingBottom(0);
        FVBox39.setMarginTop(0);
        FVBox39.setMarginLeft(13);
        FVBox39.setMarginRight(0);
        FVBox39.setMarginBottom(0);
        FVBox39.setSpacing(0);
        FVBox39.setFlexVflex("ftMin");
        FVBox39.setFlexHflex("ftMin");
        FVBox39.setScrollable(false);
        FVBox39.setBoxShadowConfigHorizontalLength(10);
        FVBox39.setBoxShadowConfigVerticalLength(10);
        FVBox39.setBoxShadowConfigBlurRadius(5);
        FVBox39.setBoxShadowConfigSpreadRadius(0);
        FVBox39.setBoxShadowConfigShadowColor("clBlack");
        FVBox39.setBoxShadowConfigOpacity(75);
        FVBox73.addChildren(FVBox39);
        FVBox39.applyProperties();
    }

    public TFLabel FLabel21 = new TFLabel();

    private void init_FLabel21() {
        FLabel21.setName("FLabel21");
        FLabel21.setLeft(0);
        FLabel21.setTop(0);
        FLabel21.setWidth(66);
        FLabel21.setHeight(13);
        FLabel21.setCaption("Aliq. Redu\u00E7\u00E3o");
        FLabel21.setFontColor("clWindowText");
        FLabel21.setFontSize(-11);
        FLabel21.setFontName("Tahoma");
        FLabel21.setFontStyle("[]");
        FLabel21.setVerticalAlignment("taVerticalCenter");
        FLabel21.setWordBreak(false);
        FVBox39.addChildren(FLabel21);
        FLabel21.applyProperties();
    }

    public TFHBox FHBox25 = new TFHBox();

    private void init_FHBox25() {
        FHBox25.setName("FHBox25");
        FHBox25.setLeft(0);
        FHBox25.setTop(14);
        FHBox25.setWidth(79);
        FHBox25.setHeight(41);
        FHBox25.setBorderStyle("stNone");
        FHBox25.setPaddingTop(0);
        FHBox25.setPaddingLeft(0);
        FHBox25.setPaddingRight(0);
        FHBox25.setPaddingBottom(0);
        FHBox25.setMarginTop(0);
        FHBox25.setMarginLeft(0);
        FHBox25.setMarginRight(0);
        FHBox25.setMarginBottom(0);
        FHBox25.setSpacing(5);
        FHBox25.setFlexVflex("ftTrue");
        FHBox25.setFlexHflex("ftFalse");
        FHBox25.setScrollable(false);
        FHBox25.setBoxShadowConfigHorizontalLength(10);
        FHBox25.setBoxShadowConfigVerticalLength(10);
        FHBox25.setBoxShadowConfigBlurRadius(5);
        FHBox25.setBoxShadowConfigSpreadRadius(0);
        FHBox25.setBoxShadowConfigShadowColor("clBlack");
        FHBox25.setBoxShadowConfigOpacity(75);
        FHBox25.setVAlign("tvTop");
        FVBox39.addChildren(FHBox25);
        FHBox25.applyProperties();
    }

    public TFDecimal FDecAliqRed = new TFDecimal();

    private void init_FDecAliqRed() {
        FDecAliqRed.setName("FDecAliqRed");
        FDecAliqRed.setLeft(0);
        FDecAliqRed.setTop(0);
        FDecAliqRed.setWidth(62);
        FDecAliqRed.setHeight(24);
        FDecAliqRed.setFlex(true);
        FDecAliqRed.setRequired(true);
        FDecAliqRed.setConstraintExpression("value < 0");
        FDecAliqRed.setConstraintCheckWhen("cwImmediate");
        FDecAliqRed.setConstraintCheckType("ctExpression");
        FDecAliqRed.setConstraintFocusOnError(false);
        FDecAliqRed.setConstraintEnableUI(true);
        FDecAliqRed.setConstraintEnabled(true);
        FDecAliqRed.setConstraintFormCheck(true);
        FDecAliqRed.setMaxlength(5);
        FDecAliqRed.setPrecision(2);
        FDecAliqRed.setFormat(",##0.00");
        FDecAliqRed.setFontColor("clWindowText");
        FDecAliqRed.setFontSize(-13);
        FDecAliqRed.setFontName("Tahoma");
        FDecAliqRed.setFontStyle("[]");
        FDecAliqRed.setAlignment("taRightJustify");
        FDecAliqRed.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FDecAliqRedExit(event);
            processarFlow("FrmServicos", "FDecAliqRed", "OnExit");
        });
        FHBox25.addChildren(FDecAliqRed);
        FDecAliqRed.applyProperties();
        addValidatable(FDecAliqRed);
    }

    public TFLabel FLabel22 = new TFLabel();

    private void init_FLabel22() {
        FLabel22.setName("FLabel22");
        FLabel22.setLeft(62);
        FLabel22.setTop(0);
        FLabel22.setWidth(11);
        FLabel22.setHeight(13);
        FLabel22.setCaption("%");
        FLabel22.setFontColor("clWindowText");
        FLabel22.setFontSize(-11);
        FLabel22.setFontName("Tahoma");
        FLabel22.setFontStyle("[]");
        FLabel22.setVerticalAlignment("taVerticalCenter");
        FLabel22.setWordBreak(false);
        FHBox25.addChildren(FLabel22);
        FLabel22.applyProperties();
    }

    public TFVBox FVBox35 = new TFVBox();

    private void init_FVBox35() {
        FVBox35.setName("FVBox35");
        FVBox35.setLeft(286);
        FVBox35.setTop(0);
        FVBox35.setWidth(275);
        FVBox35.setHeight(366);
        FVBox35.setBorderStyle("stNone");
        FVBox35.setPaddingTop(0);
        FVBox35.setPaddingLeft(0);
        FVBox35.setPaddingRight(0);
        FVBox35.setPaddingBottom(0);
        FVBox35.setMarginTop(0);
        FVBox35.setMarginLeft(0);
        FVBox35.setMarginRight(0);
        FVBox35.setMarginBottom(0);
        FVBox35.setSpacing(5);
        FVBox35.setFlexVflex("ftTrue");
        FVBox35.setFlexHflex("ftTrue");
        FVBox35.setScrollable(false);
        FVBox35.setBoxShadowConfigHorizontalLength(10);
        FVBox35.setBoxShadowConfigVerticalLength(10);
        FVBox35.setBoxShadowConfigBlurRadius(5);
        FVBox35.setBoxShadowConfigSpreadRadius(0);
        FVBox35.setBoxShadowConfigShadowColor("clBlack");
        FVBox35.setBoxShadowConfigOpacity(75);
        FHBox21.addChildren(FVBox35);
        FVBox35.applyProperties();
    }

    public TFHBox FHBox36 = new TFHBox();

    private void init_FHBox36() {
        FHBox36.setName("FHBox36");
        FHBox36.setLeft(0);
        FHBox36.setTop(0);
        FHBox36.setWidth(273);
        FHBox36.setHeight(194);
        FHBox36.setBorderStyle("stNone");
        FHBox36.setPaddingTop(0);
        FHBox36.setPaddingLeft(0);
        FHBox36.setPaddingRight(0);
        FHBox36.setPaddingBottom(0);
        FHBox36.setMarginTop(0);
        FHBox36.setMarginLeft(0);
        FHBox36.setMarginRight(0);
        FHBox36.setMarginBottom(0);
        FHBox36.setSpacing(1);
        FHBox36.setFlexVflex("ftTrue");
        FHBox36.setFlexHflex("ftTrue");
        FHBox36.setScrollable(false);
        FHBox36.setBoxShadowConfigHorizontalLength(10);
        FHBox36.setBoxShadowConfigVerticalLength(10);
        FHBox36.setBoxShadowConfigBlurRadius(5);
        FHBox36.setBoxShadowConfigSpreadRadius(0);
        FHBox36.setBoxShadowConfigShadowColor("clBlack");
        FHBox36.setBoxShadowConfigOpacity(75);
        FHBox36.setVAlign("tvTop");
        FVBox35.addChildren(FHBox36);
        FHBox36.applyProperties();
    }

    public TFVBox FVBox32 = new TFVBox();

    private void init_FVBox32() {
        FVBox32.setName("FVBox32");
        FVBox32.setLeft(0);
        FVBox32.setTop(0);
        FVBox32.setWidth(268);
        FVBox32.setHeight(188);
        FVBox32.setBorderStyle("stNone");
        FVBox32.setPaddingTop(0);
        FVBox32.setPaddingLeft(0);
        FVBox32.setPaddingRight(0);
        FVBox32.setPaddingBottom(0);
        FVBox32.setMarginTop(0);
        FVBox32.setMarginLeft(0);
        FVBox32.setMarginRight(0);
        FVBox32.setMarginBottom(0);
        FVBox32.setSpacing(1);
        FVBox32.setFlexVflex("ftTrue");
        FVBox32.setFlexHflex("ftTrue");
        FVBox32.setScrollable(false);
        FVBox32.setBoxShadowConfigHorizontalLength(10);
        FVBox32.setBoxShadowConfigVerticalLength(10);
        FVBox32.setBoxShadowConfigBlurRadius(5);
        FVBox32.setBoxShadowConfigSpreadRadius(0);
        FVBox32.setBoxShadowConfigShadowColor("clBlack");
        FVBox32.setBoxShadowConfigOpacity(75);
        FHBox36.addChildren(FVBox32);
        FVBox32.applyProperties();
    }

    public TFLabel FLabel33 = new TFLabel();

    private void init_FLabel33() {
        FLabel33.setName("FLabel33");
        FLabel33.setLeft(0);
        FLabel33.setTop(0);
        FLabel33.setWidth(30);
        FLabel33.setHeight(16);
        FLabel33.setCaption("NFSe");
        FLabel33.setFontColor("clRed");
        FLabel33.setFontSize(-13);
        FLabel33.setFontName("Tahoma");
        FLabel33.setFontStyle("[]");
        FLabel33.setVerticalAlignment("taVerticalCenter");
        FLabel33.setWordBreak(false);
        FVBox32.addChildren(FLabel33);
        FLabel33.applyProperties();
    }

    public TFGrid gridServicosPrefeitura = new TFGrid();

    private void init_gridServicosPrefeitura() {
        gridServicosPrefeitura.setName("gridServicosPrefeitura");
        gridServicosPrefeitura.setLeft(0);
        gridServicosPrefeitura.setTop(17);
        gridServicosPrefeitura.setWidth(249);
        gridServicosPrefeitura.setHeight(153);
        gridServicosPrefeitura.setTable(tbServicosPrefeitura);
        gridServicosPrefeitura.setFlexVflex("ftTrue");
        gridServicosPrefeitura.setFlexHflex("ftTrue");
        gridServicosPrefeitura.setPagingEnabled(false);
        gridServicosPrefeitura.setFrozenColumns(0);
        gridServicosPrefeitura.setShowFooter(false);
        gridServicosPrefeitura.setShowHeader(true);
        gridServicosPrefeitura.setMultiSelection(false);
        gridServicosPrefeitura.setGroupingEnabled(false);
        gridServicosPrefeitura.setGroupingExpanded(false);
        gridServicosPrefeitura.setGroupingShowFooter(false);
        gridServicosPrefeitura.setCrosstabEnabled(false);
        gridServicosPrefeitura.setCrosstabGroupType("cgtConcat");
        gridServicosPrefeitura.setEditionEnabled(false);
        gridServicosPrefeitura.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("EMPRESA");
        item0.setTitleCaption("Empresa");
        item0.setWidth(60);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridServicosPrefeitura.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("COD_SERVICO_PREFEITURA");
        item1.setTitleCaption("Codigo");
        item1.setWidth(100);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridServicosPrefeitura.getColumns().add(item1);
        FVBox32.addChildren(gridServicosPrefeitura);
        gridServicosPrefeitura.applyProperties();
    }

    public TFHBox FHBox37 = new TFHBox();

    private void init_FHBox37() {
        FHBox37.setName("FHBox37");
        FHBox37.setLeft(0);
        FHBox37.setTop(195);
        FHBox37.setWidth(273);
        FHBox37.setHeight(168);
        FHBox37.setBorderStyle("stNone");
        FHBox37.setPaddingTop(0);
        FHBox37.setPaddingLeft(0);
        FHBox37.setPaddingRight(0);
        FHBox37.setPaddingBottom(0);
        FHBox37.setMarginTop(0);
        FHBox37.setMarginLeft(0);
        FHBox37.setMarginRight(0);
        FHBox37.setMarginBottom(0);
        FHBox37.setSpacing(1);
        FHBox37.setFlexVflex("ftTrue");
        FHBox37.setFlexHflex("ftTrue");
        FHBox37.setScrollable(false);
        FHBox37.setBoxShadowConfigHorizontalLength(10);
        FHBox37.setBoxShadowConfigVerticalLength(10);
        FHBox37.setBoxShadowConfigBlurRadius(5);
        FHBox37.setBoxShadowConfigSpreadRadius(0);
        FHBox37.setBoxShadowConfigShadowColor("clBlack");
        FHBox37.setBoxShadowConfigOpacity(75);
        FHBox37.setVAlign("tvTop");
        FVBox35.addChildren(FHBox37);
        FHBox37.applyProperties();
    }

    public TFVBox FVBox72 = new TFVBox();

    private void init_FVBox72() {
        FVBox72.setName("FVBox72");
        FVBox72.setLeft(0);
        FVBox72.setTop(0);
        FVBox72.setWidth(265);
        FVBox72.setHeight(164);
        FVBox72.setBorderStyle("stNone");
        FVBox72.setPaddingTop(0);
        FVBox72.setPaddingLeft(0);
        FVBox72.setPaddingRight(0);
        FVBox72.setPaddingBottom(0);
        FVBox72.setMarginTop(0);
        FVBox72.setMarginLeft(0);
        FVBox72.setMarginRight(0);
        FVBox72.setMarginBottom(0);
        FVBox72.setSpacing(1);
        FVBox72.setFlexVflex("ftTrue");
        FVBox72.setFlexHflex("ftTrue");
        FVBox72.setScrollable(false);
        FVBox72.setBoxShadowConfigHorizontalLength(10);
        FVBox72.setBoxShadowConfigVerticalLength(10);
        FVBox72.setBoxShadowConfigBlurRadius(5);
        FVBox72.setBoxShadowConfigSpreadRadius(0);
        FVBox72.setBoxShadowConfigShadowColor("clBlack");
        FVBox72.setBoxShadowConfigOpacity(75);
        FHBox37.addChildren(FVBox72);
        FVBox72.applyProperties();
    }

    public TFLabel FLabel34 = new TFLabel();

    private void init_FLabel34() {
        FLabel34.setName("FLabel34");
        FLabel34.setLeft(0);
        FLabel34.setTop(0);
        FLabel34.setWidth(122);
        FLabel34.setHeight(16);
        FLabel34.setCaption("Redu\u00E7\u00E3o de Base ISS");
        FLabel34.setFontColor("clRed");
        FLabel34.setFontSize(-13);
        FLabel34.setFontName("Tahoma");
        FLabel34.setFontStyle("[]");
        FLabel34.setVerticalAlignment("taVerticalCenter");
        FLabel34.setWordBreak(false);
        FVBox72.addChildren(FLabel34);
        FLabel34.applyProperties();
    }

    public TFGrid gridServicosRedBcIss = new TFGrid();

    private void init_gridServicosRedBcIss() {
        gridServicosRedBcIss.setName("gridServicosRedBcIss");
        gridServicosRedBcIss.setLeft(0);
        gridServicosRedBcIss.setTop(17);
        gridServicosRedBcIss.setWidth(249);
        gridServicosRedBcIss.setHeight(134);
        gridServicosRedBcIss.setTable(tbServicosRedBcIss);
        gridServicosRedBcIss.setFlexVflex("ftTrue");
        gridServicosRedBcIss.setFlexHflex("ftTrue");
        gridServicosRedBcIss.setPagingEnabled(false);
        gridServicosRedBcIss.setFrozenColumns(0);
        gridServicosRedBcIss.setShowFooter(false);
        gridServicosRedBcIss.setShowHeader(true);
        gridServicosRedBcIss.setMultiSelection(false);
        gridServicosRedBcIss.setGroupingEnabled(false);
        gridServicosRedBcIss.setGroupingExpanded(false);
        gridServicosRedBcIss.setGroupingShowFooter(false);
        gridServicosRedBcIss.setCrosstabEnabled(false);
        gridServicosRedBcIss.setCrosstabGroupType("cgtConcat");
        gridServicosRedBcIss.setEditionEnabled(false);
        gridServicosRedBcIss.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("EMPRESA");
        item0.setTitleCaption("Empresa");
        item0.setWidth(60);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridServicosRedBcIss.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("ALIQ_RED_BC_ISS");
        item1.setTitleCaption("Aliquota");
        item1.setWidth(60);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taRight");
        item1.setFieldType("ftDecimal");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFDecimal");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridServicosRedBcIss.getColumns().add(item1);
        FVBox72.addChildren(gridServicosRedBcIss);
        gridServicosRedBcIss.applyProperties();
    }

    public TFTabsheet FTabAdicionais = new TFTabsheet();

    private void init_FTabAdicionais() {
        FTabAdicionais.setName("FTabAdicionais");
        FTabAdicionais.setCaption("Adic.");
        FTabAdicionais.setVisible(true);
        FTabAdicionais.setClosable(false);
        pageControlServico.addChildren(FTabAdicionais);
        FTabAdicionais.applyProperties();
    }

    public TFVBox FVBox40 = new TFVBox();

    private void init_FVBox40() {
        FVBox40.setName("FVBox40");
        FVBox40.setLeft(0);
        FVBox40.setTop(0);
        FVBox40.setWidth(546);
        FVBox40.setHeight(772);
        FVBox40.setAlign("alClient");
        FVBox40.setBorderStyle("stNone");
        FVBox40.setPaddingTop(0);
        FVBox40.setPaddingLeft(5);
        FVBox40.setPaddingRight(5);
        FVBox40.setPaddingBottom(0);
        FVBox40.setMarginTop(0);
        FVBox40.setMarginLeft(0);
        FVBox40.setMarginRight(0);
        FVBox40.setMarginBottom(0);
        FVBox40.setSpacing(5);
        FVBox40.setFlexVflex("ftTrue");
        FVBox40.setFlexHflex("ftTrue");
        FVBox40.setScrollable(true);
        FVBox40.setBoxShadowConfigHorizontalLength(10);
        FVBox40.setBoxShadowConfigVerticalLength(10);
        FVBox40.setBoxShadowConfigBlurRadius(5);
        FVBox40.setBoxShadowConfigSpreadRadius(0);
        FVBox40.setBoxShadowConfigShadowColor("clBlack");
        FVBox40.setBoxShadowConfigOpacity(75);
        FTabAdicionais.addChildren(FVBox40);
        FVBox40.applyProperties();
    }

    public TFVBox FVBox41 = new TFVBox();

    private void init_FVBox41() {
        FVBox41.setName("FVBox41");
        FVBox41.setLeft(0);
        FVBox41.setTop(0);
        FVBox41.setWidth(569);
        FVBox41.setHeight(249);
        FVBox41.setBorderStyle("stNone");
        FVBox41.setPaddingTop(0);
        FVBox41.setPaddingLeft(0);
        FVBox41.setPaddingRight(0);
        FVBox41.setPaddingBottom(0);
        FVBox41.setMarginTop(0);
        FVBox41.setMarginLeft(0);
        FVBox41.setMarginRight(0);
        FVBox41.setMarginBottom(0);
        FVBox41.setSpacing(0);
        FVBox41.setFlexVflex("ftFalse");
        FVBox41.setFlexHflex("ftTrue");
        FVBox41.setScrollable(false);
        FVBox41.setBoxShadowConfigHorizontalLength(10);
        FVBox41.setBoxShadowConfigVerticalLength(10);
        FVBox41.setBoxShadowConfigBlurRadius(5);
        FVBox41.setBoxShadowConfigSpreadRadius(0);
        FVBox41.setBoxShadowConfigShadowColor("clBlack");
        FVBox41.setBoxShadowConfigOpacity(75);
        FVBox40.addChildren(FVBox41);
        FVBox41.applyProperties();
    }

    public TFLabel lblDescServAd = new TFLabel();

    private void init_lblDescServAd() {
        lblDescServAd.setName("lblDescServAd");
        lblDescServAd.setLeft(0);
        lblDescServAd.setTop(0);
        lblDescServAd.setWidth(180);
        lblDescServAd.setHeight(24);
        lblDescServAd.setAlign("alTop");
        lblDescServAd.setCaption("Descri\u00E7\u00E3o Servi\u00E7o....");
        lblDescServAd.setFontColor("clHotLight");
        lblDescServAd.setFontSize(-20);
        lblDescServAd.setFontName("Tahoma");
        lblDescServAd.setFontStyle("[]");
        lblDescServAd.setVisible(false);
        lblDescServAd.setVerticalAlignment("taVerticalCenter");
        lblDescServAd.setWordBreak(false);
        FVBox41.addChildren(lblDescServAd);
        lblDescServAd.applyProperties();
    }

    public TFGrid FGridAdicionais = new TFGrid();

    private void init_FGridAdicionais() {
        FGridAdicionais.setName("FGridAdicionais");
        FGridAdicionais.setLeft(0);
        FGridAdicionais.setTop(25);
        FGridAdicionais.setWidth(541);
        FGridAdicionais.setHeight(212);
        FGridAdicionais.setAlign("alTop");
        FGridAdicionais.setTable(tbServicosAdicionais);
        FGridAdicionais.setFlexVflex("ftTrue");
        FGridAdicionais.setFlexHflex("ftTrue");
        FGridAdicionais.setPagingEnabled(false);
        FGridAdicionais.setFrozenColumns(0);
        FGridAdicionais.setShowFooter(false);
        FGridAdicionais.setShowHeader(true);
        FGridAdicionais.setMultiSelection(false);
        FGridAdicionais.setGroupingEnabled(false);
        FGridAdicionais.setGroupingExpanded(false);
        FGridAdicionais.setGroupingShowFooter(false);
        FGridAdicionais.setCrosstabEnabled(false);
        FGridAdicionais.setCrosstabGroupType("cgtConcat");
        FGridAdicionais.setEditionEnabled(false);
        FGridAdicionais.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO_ADICIONAL");
        item0.setTitleCaption("Descri\u00E7\u00E3o");
        item0.setWidth(160);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(true);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        FGridAdicionais.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("TEMPO_ADICIONAL");
        item1.setTitleCaption("TMO Padr\u00E3o");
        item1.setWidth(110);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taRight");
        item1.setFieldType("ftDecimal");
        item1.setFlexRatio(0);
        item1.setSort(true);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        TFMaskExpression item2 = new TFMaskExpression();
        item2.setExpression("*");
        item2.setEvalType("etExpression");
        item2.setMask("R$ ,##0.00");
        item2.setPadLength(0);
        item2.setPadDirection("pdNone");
        item2.setMaskType("mtDecimal");
        item1.getMasks().add(item2);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFDecimal");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        FGridAdicionais.getColumns().add(item1);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("PRECO_VENDA");
        item3.setTitleCaption("Pre\u00E7o Venda");
        item3.setWidth(110);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taRight");
        item3.setFieldType("ftDecimal");
        item3.setFlexRatio(0);
        item3.setSort(true);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        TFMaskExpression item4 = new TFMaskExpression();
        item4.setExpression("*");
        item4.setEvalType("etExpression");
        item4.setMask("R$ ,##0.00");
        item4.setPadLength(0);
        item4.setPadDirection("pdNone");
        item4.setMaskType("mtDecimal");
        item3.getMasks().add(item4);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFDecimal");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        FGridAdicionais.getColumns().add(item3);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("PRECO_CUSTO");
        item5.setTitleCaption("Pre\u00E7o Custo");
        item5.setWidth(110);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taRight");
        item5.setFieldType("ftDecimal");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        TFMaskExpression item6 = new TFMaskExpression();
        item6.setExpression("*");
        item6.setEvalType("etExpression");
        item6.setMask("R$ ,##0.00");
        item6.setPadLength(0);
        item6.setPadDirection("pdNone");
        item6.setMaskType("mtDecimal");
        item5.getMasks().add(item6);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFDecimal");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        FGridAdicionais.getColumns().add(item5);
        TFGridColumn item7 = new TFGridColumn();
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        TFImageExpression item8 = new TFImageExpression();
        item8.setExpression("*");
        item8.setEvalType("etExpression");
        item8.setImageId(8);
        item8.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FGridAdicionaisExcluirAdClick(event);
            processarFlow("FrmServicos", "item8", "OnClick");
        });
        item7.getImages().add(item8);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        FGridAdicionais.getColumns().add(item7);
        FVBox41.addChildren(FGridAdicionais);
        FGridAdicionais.applyProperties();
    }

    public TFHBox FHBox22 = new TFHBox();

    private void init_FHBox22() {
        FHBox22.setName("FHBox22");
        FHBox22.setLeft(0);
        FHBox22.setTop(250);
        FHBox22.setWidth(345);
        FHBox22.setHeight(41);
        FHBox22.setBorderStyle("stNone");
        FHBox22.setPaddingTop(0);
        FHBox22.setPaddingLeft(0);
        FHBox22.setPaddingRight(0);
        FHBox22.setPaddingBottom(0);
        FHBox22.setMarginTop(0);
        FHBox22.setMarginLeft(0);
        FHBox22.setMarginRight(0);
        FHBox22.setMarginBottom(0);
        FHBox22.setSpacing(5);
        FHBox22.setFlexVflex("ftMin");
        FHBox22.setFlexHflex("ftTrue");
        FHBox22.setScrollable(false);
        FHBox22.setBoxShadowConfigHorizontalLength(10);
        FHBox22.setBoxShadowConfigVerticalLength(10);
        FHBox22.setBoxShadowConfigBlurRadius(5);
        FHBox22.setBoxShadowConfigSpreadRadius(0);
        FHBox22.setBoxShadowConfigShadowColor("clBlack");
        FHBox22.setBoxShadowConfigOpacity(75);
        FHBox22.setVAlign("tvTop");
        FVBox40.addChildren(FHBox22);
        FHBox22.applyProperties();
    }

    public TFButton btnNovoAd = new TFButton();

    private void init_btnNovoAd() {
        btnNovoAd.setName("btnNovoAd");
        btnNovoAd.setLeft(0);
        btnNovoAd.setTop(0);
        btnNovoAd.setWidth(40);
        btnNovoAd.setHeight(40);
        btnNovoAd.setFontColor("clWindowText");
        btnNovoAd.setFontSize(-11);
        btnNovoAd.setFontName("Tahoma");
        btnNovoAd.setFontStyle("[]");
        btnNovoAd.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoAdClick(event);
            processarFlow("FrmServicos", "btnNovoAd", "OnClick");
        });
        btnNovoAd.setImageId(6);
        btnNovoAd.setColor("clBtnFace");
        btnNovoAd.setAccess(false);
        btnNovoAd.setIconReverseDirection(false);
        FHBox22.addChildren(btnNovoAd);
        btnNovoAd.applyProperties();
    }

    public TFButton btnAlterarrAd = new TFButton();

    private void init_btnAlterarrAd() {
        btnAlterarrAd.setName("btnAlterarrAd");
        btnAlterarrAd.setLeft(40);
        btnAlterarrAd.setTop(0);
        btnAlterarrAd.setWidth(40);
        btnAlterarrAd.setHeight(40);
        btnAlterarrAd.setFontColor("clWindowText");
        btnAlterarrAd.setFontSize(-11);
        btnAlterarrAd.setFontName("Tahoma");
        btnAlterarrAd.setFontStyle("[]");
        btnAlterarrAd.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarAdClick(event);
            processarFlow("FrmServicos", "btnAlterarrAd", "OnClick");
        });
        btnAlterarrAd.setImageId(7);
        btnAlterarrAd.setColor("clBtnFace");
        btnAlterarrAd.setAccess(false);
        btnAlterarrAd.setIconReverseDirection(false);
        FHBox22.addChildren(btnAlterarrAd);
        btnAlterarrAd.applyProperties();
    }

    public TFButton btnSalvarAd = new TFButton();

    private void init_btnSalvarAd() {
        btnSalvarAd.setName("btnSalvarAd");
        btnSalvarAd.setLeft(80);
        btnSalvarAd.setTop(0);
        btnSalvarAd.setWidth(40);
        btnSalvarAd.setHeight(40);
        btnSalvarAd.setFontColor("clWindowText");
        btnSalvarAd.setFontSize(-11);
        btnSalvarAd.setFontName("Tahoma");
        btnSalvarAd.setFontStyle("[]");
        btnSalvarAd.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarAdClick(event);
            processarFlow("FrmServicos", "btnSalvarAd", "OnClick");
        });
        btnSalvarAd.setImageId(4);
        btnSalvarAd.setColor("clBtnFace");
        btnSalvarAd.setAccess(false);
        btnSalvarAd.setIconReverseDirection(false);
        FHBox22.addChildren(btnSalvarAd);
        btnSalvarAd.applyProperties();
    }

    public TFButton btnCancelAd = new TFButton();

    private void init_btnCancelAd() {
        btnCancelAd.setName("btnCancelAd");
        btnCancelAd.setLeft(120);
        btnCancelAd.setTop(0);
        btnCancelAd.setWidth(40);
        btnCancelAd.setHeight(40);
        btnCancelAd.setFontColor("clWindowText");
        btnCancelAd.setFontSize(-11);
        btnCancelAd.setFontName("Tahoma");
        btnCancelAd.setFontStyle("[]");
        btnCancelAd.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelAdClick(event);
            processarFlow("FrmServicos", "btnCancelAd", "OnClick");
        });
        btnCancelAd.setImageId(9);
        btnCancelAd.setColor("clBtnFace");
        btnCancelAd.setAccess(false);
        btnCancelAd.setIconReverseDirection(false);
        FHBox22.addChildren(btnCancelAd);
        btnCancelAd.applyProperties();
    }

    public TFVBox FVBox42 = new TFVBox();

    private void init_FVBox42() {
        FVBox42.setName("FVBox42");
        FVBox42.setLeft(0);
        FVBox42.setTop(292);
        FVBox42.setWidth(570);
        FVBox42.setHeight(73);
        FVBox42.setBorderStyle("stNone");
        FVBox42.setPaddingTop(0);
        FVBox42.setPaddingLeft(0);
        FVBox42.setPaddingRight(0);
        FVBox42.setPaddingBottom(0);
        FVBox42.setMarginTop(0);
        FVBox42.setMarginLeft(0);
        FVBox42.setMarginRight(0);
        FVBox42.setMarginBottom(0);
        FVBox42.setSpacing(5);
        FVBox42.setFlexVflex("ftMin");
        FVBox42.setFlexHflex("ftTrue");
        FVBox42.setScrollable(false);
        FVBox42.setBoxShadowConfigHorizontalLength(10);
        FVBox42.setBoxShadowConfigVerticalLength(10);
        FVBox42.setBoxShadowConfigBlurRadius(5);
        FVBox42.setBoxShadowConfigSpreadRadius(0);
        FVBox42.setBoxShadowConfigShadowColor("clBlack");
        FVBox42.setBoxShadowConfigOpacity(75);
        FVBox40.addChildren(FVBox42);
        FVBox42.applyProperties();
    }

    public TFLabel FLabel23 = new TFLabel();

    private void init_FLabel23() {
        FLabel23.setName("FLabel23");
        FLabel23.setLeft(0);
        FLabel23.setTop(0);
        FLabel23.setWidth(136);
        FLabel23.setHeight(13);
        FLabel23.setCaption("Servi\u00E7o adicional (Descri\u00E7\u00E3o)");
        FLabel23.setFontColor("clWindowText");
        FLabel23.setFontSize(-11);
        FLabel23.setFontName("Tahoma");
        FLabel23.setFontStyle("[]");
        FLabel23.setVerticalAlignment("taVerticalCenter");
        FLabel23.setWordBreak(false);
        FVBox42.addChildren(FLabel23);
        FLabel23.applyProperties();
    }

    public TFString FString2 = new TFString();

    private void init_FString2() {
        FString2.setName("FString2");
        FString2.setLeft(0);
        FString2.setTop(14);
        FString2.setWidth(377);
        FString2.setHeight(24);
        FString2.setTable(tbServicosAdicionais);
        FString2.setFieldName("DESCRICAO_ADICIONAL");
        FString2.setFlex(true);
        FString2.setRequired(true);
        FString2.setConstraintCheckWhen("cwImmediate");
        FString2.setConstraintCheckType("ctExpression");
        FString2.setConstraintFocusOnError(false);
        FString2.setConstraintEnableUI(true);
        FString2.setConstraintEnabled(false);
        FString2.setConstraintFormCheck(true);
        FString2.setCharCase("ccNormal");
        FString2.setPwd(false);
        FString2.setMaxlength(60);
        FString2.setFontColor("clWindowText");
        FString2.setFontSize(-13);
        FString2.setFontName("Tahoma");
        FString2.setFontStyle("[]");
        FString2.setSaveLiteralCharacter(false);
        FString2.applyProperties();
        FVBox42.addChildren(FString2);
        addValidatable(FString2);
    }

    public TFHBox FHBox23 = new TFHBox();

    private void init_FHBox23() {
        FHBox23.setName("FHBox23");
        FHBox23.setLeft(0);
        FHBox23.setTop(366);
        FHBox23.setWidth(570);
        FHBox23.setHeight(117);
        FHBox23.setBorderStyle("stNone");
        FHBox23.setPaddingTop(0);
        FHBox23.setPaddingLeft(0);
        FHBox23.setPaddingRight(0);
        FHBox23.setPaddingBottom(0);
        FHBox23.setMarginTop(0);
        FHBox23.setMarginLeft(0);
        FHBox23.setMarginRight(0);
        FHBox23.setMarginBottom(0);
        FHBox23.setSpacing(5);
        FHBox23.setFlexVflex("ftMin");
        FHBox23.setFlexHflex("ftTrue");
        FHBox23.setScrollable(false);
        FHBox23.setBoxShadowConfigHorizontalLength(10);
        FHBox23.setBoxShadowConfigVerticalLength(10);
        FHBox23.setBoxShadowConfigBlurRadius(5);
        FHBox23.setBoxShadowConfigSpreadRadius(0);
        FHBox23.setBoxShadowConfigShadowColor("clBlack");
        FHBox23.setBoxShadowConfigOpacity(75);
        FHBox23.setVAlign("tvTop");
        FVBox40.addChildren(FHBox23);
        FHBox23.applyProperties();
    }

    public TFVBox FVBox43 = new TFVBox();

    private void init_FVBox43() {
        FVBox43.setName("FVBox43");
        FVBox43.setLeft(0);
        FVBox43.setTop(0);
        FVBox43.setWidth(200);
        FVBox43.setHeight(100);
        FVBox43.setBorderStyle("stNone");
        FVBox43.setPaddingTop(0);
        FVBox43.setPaddingLeft(0);
        FVBox43.setPaddingRight(0);
        FVBox43.setPaddingBottom(0);
        FVBox43.setMarginTop(0);
        FVBox43.setMarginLeft(0);
        FVBox43.setMarginRight(0);
        FVBox43.setMarginBottom(0);
        FVBox43.setSpacing(5);
        FVBox43.setFlexVflex("ftTrue");
        FVBox43.setFlexHflex("ftTrue");
        FVBox43.setScrollable(false);
        FVBox43.setBoxShadowConfigHorizontalLength(10);
        FVBox43.setBoxShadowConfigVerticalLength(10);
        FVBox43.setBoxShadowConfigBlurRadius(5);
        FVBox43.setBoxShadowConfigSpreadRadius(0);
        FVBox43.setBoxShadowConfigShadowColor("clBlack");
        FVBox43.setBoxShadowConfigOpacity(75);
        FHBox23.addChildren(FVBox43);
        FVBox43.applyProperties();
    }

    public TFLabel FLabel24 = new TFLabel();

    private void init_FLabel24() {
        FLabel24.setName("FLabel24");
        FLabel24.setLeft(0);
        FLabel24.setTop(0);
        FLabel24.setWidth(59);
        FLabel24.setHeight(13);
        FLabel24.setCaption("TMO Padr\u00E3o");
        FLabel24.setFontColor("clWindowText");
        FLabel24.setFontSize(-11);
        FLabel24.setFontName("Tahoma");
        FLabel24.setFontStyle("[]");
        FLabel24.setVerticalAlignment("taVerticalCenter");
        FLabel24.setWordBreak(false);
        FVBox43.addChildren(FLabel24);
        FLabel24.applyProperties();
    }

    public TFDecimal FDecTmoAdic = new TFDecimal();

    private void init_FDecTmoAdic() {
        FDecTmoAdic.setName("FDecTmoAdic");
        FDecTmoAdic.setLeft(0);
        FDecTmoAdic.setTop(14);
        FDecTmoAdic.setWidth(121);
        FDecTmoAdic.setHeight(24);
        FDecTmoAdic.setTable(tbServicosAdicionais);
        FDecTmoAdic.setFieldName("TEMPO_ADICIONAL");
        FDecTmoAdic.setFlex(true);
        FDecTmoAdic.setRequired(true);
        FDecTmoAdic.setConstraintExpression("value < 0");
        FDecTmoAdic.setConstraintMessage("Valor negativo n\u00E3o permitido");
        FDecTmoAdic.setConstraintCheckWhen("cwImmediate");
        FDecTmoAdic.setConstraintCheckType("ctExpression");
        FDecTmoAdic.setConstraintFocusOnError(false);
        FDecTmoAdic.setConstraintEnableUI(true);
        FDecTmoAdic.setConstraintEnabled(true);
        FDecTmoAdic.setConstraintFormCheck(true);
        FDecTmoAdic.setMaxlength(0);
        FDecTmoAdic.setPrecision(2);
        FDecTmoAdic.setFontColor("clWindowText");
        FDecTmoAdic.setFontSize(-13);
        FDecTmoAdic.setFontName("Tahoma");
        FDecTmoAdic.setFontStyle("[]");
        FDecTmoAdic.setAlignment("taRightJustify");
        FVBox43.addChildren(FDecTmoAdic);
        FDecTmoAdic.applyProperties();
        addValidatable(FDecTmoAdic);
    }

    public TFVBox FVBox44 = new TFVBox();

    private void init_FVBox44() {
        FVBox44.setName("FVBox44");
        FVBox44.setLeft(200);
        FVBox44.setTop(0);
        FVBox44.setWidth(200);
        FVBox44.setHeight(100);
        FVBox44.setBorderStyle("stNone");
        FVBox44.setPaddingTop(0);
        FVBox44.setPaddingLeft(0);
        FVBox44.setPaddingRight(0);
        FVBox44.setPaddingBottom(0);
        FVBox44.setMarginTop(0);
        FVBox44.setMarginLeft(0);
        FVBox44.setMarginRight(0);
        FVBox44.setMarginBottom(0);
        FVBox44.setSpacing(5);
        FVBox44.setFlexVflex("ftTrue");
        FVBox44.setFlexHflex("ftTrue");
        FVBox44.setScrollable(false);
        FVBox44.setBoxShadowConfigHorizontalLength(10);
        FVBox44.setBoxShadowConfigVerticalLength(10);
        FVBox44.setBoxShadowConfigBlurRadius(5);
        FVBox44.setBoxShadowConfigSpreadRadius(0);
        FVBox44.setBoxShadowConfigShadowColor("clBlack");
        FVBox44.setBoxShadowConfigOpacity(75);
        FHBox23.addChildren(FVBox44);
        FVBox44.applyProperties();
    }

    public TFLabel FLabel25 = new TFLabel();

    private void init_FLabel25() {
        FLabel25.setName("FLabel25");
        FLabel25.setLeft(0);
        FLabel25.setTop(0);
        FLabel25.setWidth(60);
        FLabel25.setHeight(13);
        FLabel25.setCaption("Pre\u00E7o Venda");
        FLabel25.setFontColor("clWindowText");
        FLabel25.setFontSize(-11);
        FLabel25.setFontName("Tahoma");
        FLabel25.setFontStyle("[]");
        FLabel25.setVerticalAlignment("taVerticalCenter");
        FLabel25.setWordBreak(false);
        FVBox44.addChildren(FLabel25);
        FLabel25.applyProperties();
    }

    public TFDecimal FDecPrecVenAdic = new TFDecimal();

    private void init_FDecPrecVenAdic() {
        FDecPrecVenAdic.setName("FDecPrecVenAdic");
        FDecPrecVenAdic.setLeft(0);
        FDecPrecVenAdic.setTop(14);
        FDecPrecVenAdic.setWidth(121);
        FDecPrecVenAdic.setHeight(24);
        FDecPrecVenAdic.setTable(tbServicosAdicionais);
        FDecPrecVenAdic.setFieldName("PRECO_VENDA");
        FDecPrecVenAdic.setFlex(true);
        FDecPrecVenAdic.setRequired(false);
        FDecPrecVenAdic.setConstraintExpression("value < 0");
        FDecPrecVenAdic.setConstraintMessage("Valor negativo n\u00E3o permitido");
        FDecPrecVenAdic.setConstraintCheckWhen("cwImmediate");
        FDecPrecVenAdic.setConstraintCheckType("ctExpression");
        FDecPrecVenAdic.setConstraintFocusOnError(false);
        FDecPrecVenAdic.setConstraintEnableUI(true);
        FDecPrecVenAdic.setConstraintEnabled(true);
        FDecPrecVenAdic.setConstraintFormCheck(true);
        FDecPrecVenAdic.setMaxlength(0);
        FDecPrecVenAdic.setPrecision(2);
        FDecPrecVenAdic.setFontColor("clWindowText");
        FDecPrecVenAdic.setFontSize(-13);
        FDecPrecVenAdic.setFontName("Tahoma");
        FDecPrecVenAdic.setFontStyle("[]");
        FDecPrecVenAdic.setAlignment("taRightJustify");
        FVBox44.addChildren(FDecPrecVenAdic);
        FDecPrecVenAdic.applyProperties();
        addValidatable(FDecPrecVenAdic);
    }

    public TFVBox FVBox45 = new TFVBox();

    private void init_FVBox45() {
        FVBox45.setName("FVBox45");
        FVBox45.setLeft(400);
        FVBox45.setTop(0);
        FVBox45.setWidth(160);
        FVBox45.setHeight(100);
        FVBox45.setBorderStyle("stNone");
        FVBox45.setPaddingTop(0);
        FVBox45.setPaddingLeft(0);
        FVBox45.setPaddingRight(0);
        FVBox45.setPaddingBottom(0);
        FVBox45.setMarginTop(0);
        FVBox45.setMarginLeft(0);
        FVBox45.setMarginRight(0);
        FVBox45.setMarginBottom(0);
        FVBox45.setSpacing(5);
        FVBox45.setFlexVflex("ftFalse");
        FVBox45.setFlexHflex("ftFalse");
        FVBox45.setScrollable(false);
        FVBox45.setBoxShadowConfigHorizontalLength(10);
        FVBox45.setBoxShadowConfigVerticalLength(10);
        FVBox45.setBoxShadowConfigBlurRadius(5);
        FVBox45.setBoxShadowConfigSpreadRadius(0);
        FVBox45.setBoxShadowConfigShadowColor("clBlack");
        FVBox45.setBoxShadowConfigOpacity(75);
        FHBox23.addChildren(FVBox45);
        FVBox45.applyProperties();
    }

    public TFLabel FLabel26 = new TFLabel();

    private void init_FLabel26() {
        FLabel26.setName("FLabel26");
        FLabel26.setLeft(0);
        FLabel26.setTop(0);
        FLabel26.setWidth(58);
        FLabel26.setHeight(13);
        FLabel26.setCaption("Pre\u00E7o Custo");
        FLabel26.setFontColor("clWindowText");
        FLabel26.setFontSize(-11);
        FLabel26.setFontName("Tahoma");
        FLabel26.setFontStyle("[]");
        FLabel26.setVerticalAlignment("taVerticalCenter");
        FLabel26.setWordBreak(false);
        FVBox45.addChildren(FLabel26);
        FLabel26.applyProperties();
    }

    public TFDecimal FDecPrecoCustoAdic = new TFDecimal();

    private void init_FDecPrecoCustoAdic() {
        FDecPrecoCustoAdic.setName("FDecPrecoCustoAdic");
        FDecPrecoCustoAdic.setLeft(0);
        FDecPrecoCustoAdic.setTop(14);
        FDecPrecoCustoAdic.setWidth(121);
        FDecPrecoCustoAdic.setHeight(24);
        FDecPrecoCustoAdic.setTable(tbServicosAdicionais);
        FDecPrecoCustoAdic.setFieldName("PRECO_CUSTO");
        FDecPrecoCustoAdic.setFlex(true);
        FDecPrecoCustoAdic.setRequired(false);
        FDecPrecoCustoAdic.setConstraintExpression("value < 0");
        FDecPrecoCustoAdic.setConstraintMessage("Valor negativo n\u00E3o permitido");
        FDecPrecoCustoAdic.setConstraintCheckWhen("cwImmediate");
        FDecPrecoCustoAdic.setConstraintCheckType("ctExpression");
        FDecPrecoCustoAdic.setConstraintFocusOnError(false);
        FDecPrecoCustoAdic.setConstraintEnableUI(true);
        FDecPrecoCustoAdic.setConstraintEnabled(true);
        FDecPrecoCustoAdic.setConstraintFormCheck(true);
        FDecPrecoCustoAdic.setMaxlength(0);
        FDecPrecoCustoAdic.setPrecision(2);
        FDecPrecoCustoAdic.setFontColor("clWindowText");
        FDecPrecoCustoAdic.setFontSize(-13);
        FDecPrecoCustoAdic.setFontName("Tahoma");
        FDecPrecoCustoAdic.setFontStyle("[]");
        FDecPrecoCustoAdic.setAlignment("taRightJustify");
        FVBox45.addChildren(FDecPrecoCustoAdic);
        FDecPrecoCustoAdic.applyProperties();
        addValidatable(FDecPrecoCustoAdic);
    }

    public TFTabsheet FTabsheetModelos = new TFTabsheet();

    private void init_FTabsheetModelos() {
        FTabsheetModelos.setName("FTabsheetModelos");
        FTabsheetModelos.setCaption("Modelo");
        FTabsheetModelos.setVisible(true);
        FTabsheetModelos.setClosable(false);
        pageControlServico.addChildren(FTabsheetModelos);
        FTabsheetModelos.applyProperties();
    }

    public TFVBox FVBox46 = new TFVBox();

    private void init_FVBox46() {
        FVBox46.setName("FVBox46");
        FVBox46.setLeft(0);
        FVBox46.setTop(0);
        FVBox46.setWidth(546);
        FVBox46.setHeight(772);
        FVBox46.setAlign("alClient");
        FVBox46.setBorderStyle("stNone");
        FVBox46.setPaddingTop(0);
        FVBox46.setPaddingLeft(5);
        FVBox46.setPaddingRight(5);
        FVBox46.setPaddingBottom(0);
        FVBox46.setMarginTop(0);
        FVBox46.setMarginLeft(0);
        FVBox46.setMarginRight(0);
        FVBox46.setMarginBottom(0);
        FVBox46.setSpacing(5);
        FVBox46.setFlexVflex("ftTrue");
        FVBox46.setFlexHflex("ftTrue");
        FVBox46.setScrollable(false);
        FVBox46.setBoxShadowConfigHorizontalLength(10);
        FVBox46.setBoxShadowConfigVerticalLength(10);
        FVBox46.setBoxShadowConfigBlurRadius(5);
        FVBox46.setBoxShadowConfigSpreadRadius(0);
        FVBox46.setBoxShadowConfigShadowColor("clBlack");
        FVBox46.setBoxShadowConfigOpacity(75);
        FTabsheetModelos.addChildren(FVBox46);
        FVBox46.applyProperties();
    }

    public TFVBox FVBox47 = new TFVBox();

    private void init_FVBox47() {
        FVBox47.setName("FVBox47");
        FVBox47.setLeft(0);
        FVBox47.setTop(0);
        FVBox47.setWidth(570);
        FVBox47.setHeight(85);
        FVBox47.setBorderStyle("stNone");
        FVBox47.setPaddingTop(0);
        FVBox47.setPaddingLeft(0);
        FVBox47.setPaddingRight(0);
        FVBox47.setPaddingBottom(0);
        FVBox47.setMarginTop(0);
        FVBox47.setMarginLeft(0);
        FVBox47.setMarginRight(0);
        FVBox47.setMarginBottom(0);
        FVBox47.setSpacing(0);
        FVBox47.setFlexVflex("ftFalse");
        FVBox47.setFlexHflex("ftTrue");
        FVBox47.setScrollable(false);
        FVBox47.setBoxShadowConfigHorizontalLength(10);
        FVBox47.setBoxShadowConfigVerticalLength(10);
        FVBox47.setBoxShadowConfigBlurRadius(5);
        FVBox47.setBoxShadowConfigSpreadRadius(0);
        FVBox47.setBoxShadowConfigShadowColor("clBlack");
        FVBox47.setBoxShadowConfigOpacity(75);
        FVBox46.addChildren(FVBox47);
        FVBox47.applyProperties();
    }

    public TFLabel lblDescServicMod = new TFLabel();

    private void init_lblDescServicMod() {
        lblDescServicMod.setName("lblDescServicMod");
        lblDescServicMod.setLeft(0);
        lblDescServicMod.setTop(0);
        lblDescServicMod.setWidth(180);
        lblDescServicMod.setHeight(24);
        lblDescServicMod.setAlign("alTop");
        lblDescServicMod.setCaption("Descri\u00E7\u00E3o Servi\u00E7o....");
        lblDescServicMod.setFontColor("clHotLight");
        lblDescServicMod.setFontSize(-20);
        lblDescServicMod.setFontName("Tahoma");
        lblDescServicMod.setFontStyle("[]");
        lblDescServicMod.setVisible(false);
        lblDescServicMod.setVerticalAlignment("taVerticalCenter");
        lblDescServicMod.setWordBreak(false);
        FVBox47.addChildren(lblDescServicMod);
        lblDescServicMod.applyProperties();
    }

    public TFVBox FVBox63 = new TFVBox();

    private void init_FVBox63() {
        FVBox63.setName("FVBox63");
        FVBox63.setLeft(0);
        FVBox63.setTop(25);
        FVBox63.setWidth(568);
        FVBox63.setHeight(68);
        FVBox63.setBorderStyle("stNone");
        FVBox63.setPaddingTop(0);
        FVBox63.setPaddingLeft(0);
        FVBox63.setPaddingRight(0);
        FVBox63.setPaddingBottom(0);
        FVBox63.setMarginTop(0);
        FVBox63.setMarginLeft(0);
        FVBox63.setMarginRight(0);
        FVBox63.setMarginBottom(0);
        FVBox63.setSpacing(5);
        FVBox63.setFlexVflex("ftFalse");
        FVBox63.setFlexHflex("ftTrue");
        FVBox63.setScrollable(false);
        FVBox63.setBoxShadowConfigHorizontalLength(10);
        FVBox63.setBoxShadowConfigVerticalLength(10);
        FVBox63.setBoxShadowConfigBlurRadius(5);
        FVBox63.setBoxShadowConfigSpreadRadius(0);
        FVBox63.setBoxShadowConfigShadowColor("clBlack");
        FVBox63.setBoxShadowConfigOpacity(75);
        FVBox47.addChildren(FVBox63);
        FVBox63.applyProperties();
    }

    public TFHBox FHBox24 = new TFHBox();

    private void init_FHBox24() {
        FHBox24.setName("FHBox24");
        FHBox24.setLeft(0);
        FHBox24.setTop(0);
        FHBox24.setWidth(561);
        FHBox24.setHeight(25);
        FHBox24.setAlign("alTop");
        FHBox24.setBorderStyle("stNone");
        FHBox24.setPaddingTop(0);
        FHBox24.setPaddingLeft(0);
        FHBox24.setPaddingRight(0);
        FHBox24.setPaddingBottom(0);
        FHBox24.setMarginTop(0);
        FHBox24.setMarginLeft(0);
        FHBox24.setMarginRight(0);
        FHBox24.setMarginBottom(0);
        FHBox24.setSpacing(5);
        FHBox24.setFlexVflex("ftFalse");
        FHBox24.setFlexHflex("ftTrue");
        FHBox24.setScrollable(false);
        FHBox24.setBoxShadowConfigHorizontalLength(10);
        FHBox24.setBoxShadowConfigVerticalLength(10);
        FHBox24.setBoxShadowConfigBlurRadius(5);
        FHBox24.setBoxShadowConfigSpreadRadius(0);
        FHBox24.setBoxShadowConfigShadowColor("clBlack");
        FHBox24.setBoxShadowConfigOpacity(75);
        FHBox24.setVAlign("tvMiddle");
        FVBox63.addChildren(FHBox24);
        FHBox24.applyProperties();
    }

    public TFLabel FLabel28 = new TFLabel();

    private void init_FLabel28() {
        FLabel28.setName("FLabel28");
        FLabel28.setLeft(0);
        FLabel28.setTop(0);
        FLabel28.setWidth(387);
        FLabel28.setHeight(19);
        FLabel28.setCaption("Tempos espec\u00EDficos do servi\u00E7o para os modelos abaixo");
        FLabel28.setColor("clBtnFace");
        FLabel28.setFontColor("clBlack");
        FLabel28.setFontSize(-16);
        FLabel28.setFontName("Tahoma");
        FLabel28.setFontStyle("[]");
        FLabel28.setVerticalAlignment("taVerticalCenter");
        FLabel28.setWordBreak(false);
        FHBox24.addChildren(FLabel28);
        FLabel28.applyProperties();
    }

    public TFHBox FHBox27 = new TFHBox();

    private void init_FHBox27() {
        FHBox27.setName("FHBox27");
        FHBox27.setLeft(387);
        FHBox27.setTop(0);
        FHBox27.setWidth(165);
        FHBox27.setHeight(27);
        FHBox27.setBorderStyle("stNone");
        FHBox27.setPaddingTop(0);
        FHBox27.setPaddingLeft(0);
        FHBox27.setPaddingRight(0);
        FHBox27.setPaddingBottom(0);
        FHBox27.setMarginTop(0);
        FHBox27.setMarginLeft(0);
        FHBox27.setMarginRight(0);
        FHBox27.setMarginBottom(0);
        FHBox27.setSpacing(1);
        FHBox27.setFlexVflex("ftFalse");
        FHBox27.setFlexHflex("ftTrue");
        FHBox27.setScrollable(false);
        FHBox27.setBoxShadowConfigHorizontalLength(10);
        FHBox27.setBoxShadowConfigVerticalLength(10);
        FHBox27.setBoxShadowConfigBlurRadius(5);
        FHBox27.setBoxShadowConfigSpreadRadius(0);
        FHBox27.setBoxShadowConfigShadowColor("clBlack");
        FHBox27.setBoxShadowConfigOpacity(75);
        FHBox27.setVAlign("tvTop");
        FHBox24.addChildren(FHBox27);
        FHBox27.applyProperties();
    }

    public TFHBox FHBox28 = new TFHBox();

    private void init_FHBox28() {
        FHBox28.setName("FHBox28");
        FHBox28.setLeft(0);
        FHBox28.setTop(26);
        FHBox28.setWidth(561);
        FHBox28.setHeight(41);
        FHBox28.setAlign("alTop");
        FHBox28.setBorderStyle("stNone");
        FHBox28.setPaddingTop(0);
        FHBox28.setPaddingLeft(0);
        FHBox28.setPaddingRight(0);
        FHBox28.setPaddingBottom(0);
        FHBox28.setMarginTop(0);
        FHBox28.setMarginLeft(0);
        FHBox28.setMarginRight(0);
        FHBox28.setMarginBottom(0);
        FHBox28.setSpacing(5);
        FHBox28.setFlexVflex("ftFalse");
        FHBox28.setFlexHflex("ftTrue");
        FHBox28.setScrollable(false);
        FHBox28.setBoxShadowConfigHorizontalLength(10);
        FHBox28.setBoxShadowConfigVerticalLength(10);
        FHBox28.setBoxShadowConfigBlurRadius(5);
        FHBox28.setBoxShadowConfigSpreadRadius(0);
        FHBox28.setBoxShadowConfigShadowColor("clBlack");
        FHBox28.setBoxShadowConfigOpacity(75);
        FHBox28.setVAlign("tvTop");
        FVBox63.addChildren(FHBox28);
        FHBox28.applyProperties();
    }

    public TFCombo cbMarcaCad = new TFCombo();

    private void init_cbMarcaCad() {
        cbMarcaCad.setName("cbMarcaCad");
        cbMarcaCad.setLeft(0);
        cbMarcaCad.setTop(0);
        cbMarcaCad.setWidth(145);
        cbMarcaCad.setHeight(21);
        cbMarcaCad.setLookupTable(tbMarcas);
        cbMarcaCad.setLookupKey("COD_MARCA");
        cbMarcaCad.setLookupDesc("DESCRICAO_MARCA2");
        cbMarcaCad.setFlex(true);
        cbMarcaCad.setReadOnly(true);
        cbMarcaCad.setRequired(false);
        cbMarcaCad.setPrompt("Marca");
        cbMarcaCad.setConstraintCheckWhen("cwImmediate");
        cbMarcaCad.setConstraintCheckType("ctExpression");
        cbMarcaCad.setConstraintFocusOnError(false);
        cbMarcaCad.setConstraintEnableUI(true);
        cbMarcaCad.setConstraintEnabled(false);
        cbMarcaCad.setConstraintFormCheck(true);
        cbMarcaCad.setClearOnDelKey(true);
        cbMarcaCad.setUseClearButton(true);
        cbMarcaCad.setHideClearButtonOnNullValue(true);
        cbMarcaCad.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbMarcaCadChange(event);
            processarFlow("FrmServicos", "cbMarcaCad", "OnChange");
        });
        cbMarcaCad.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbMarcaCadClearClick(event);
            processarFlow("FrmServicos", "cbMarcaCad", "OnClearClick");
        });
        FHBox28.addChildren(cbMarcaCad);
        cbMarcaCad.applyProperties();
        addValidatable(cbMarcaCad);
    }

    public TFCombo cbFamiliaCad = new TFCombo();

    private void init_cbFamiliaCad() {
        cbFamiliaCad.setName("cbFamiliaCad");
        cbFamiliaCad.setLeft(145);
        cbFamiliaCad.setTop(0);
        cbFamiliaCad.setWidth(145);
        cbFamiliaCad.setHeight(21);
        cbFamiliaCad.setLookupTable(tbProdutos);
        cbFamiliaCad.setLookupKey("COD_PRODUTO");
        cbFamiliaCad.setLookupDesc("DESC_PRODUTO_INIT");
        cbFamiliaCad.setFlex(true);
        cbFamiliaCad.setReadOnly(true);
        cbFamiliaCad.setRequired(false);
        cbFamiliaCad.setPrompt("Fam\u00EDlia");
        cbFamiliaCad.setConstraintCheckWhen("cwImmediate");
        cbFamiliaCad.setConstraintCheckType("ctExpression");
        cbFamiliaCad.setConstraintFocusOnError(false);
        cbFamiliaCad.setConstraintEnableUI(true);
        cbFamiliaCad.setConstraintEnabled(false);
        cbFamiliaCad.setConstraintFormCheck(true);
        cbFamiliaCad.setClearOnDelKey(true);
        cbFamiliaCad.setUseClearButton(true);
        cbFamiliaCad.setHideClearButtonOnNullValue(true);
        cbFamiliaCad.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbFamiliaCadChange(event);
            processarFlow("FrmServicos", "cbFamiliaCad", "OnChange");
        });
        cbFamiliaCad.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbFamiliaCadClearClick(event);
            processarFlow("FrmServicos", "cbFamiliaCad", "OnClearClick");
        });
        FHBox28.addChildren(cbFamiliaCad);
        cbFamiliaCad.applyProperties();
        addValidatable(cbFamiliaCad);
    }

    public TFCombo cbModeloCad = new TFCombo();

    private void init_cbModeloCad() {
        cbModeloCad.setName("cbModeloCad");
        cbModeloCad.setLeft(290);
        cbModeloCad.setTop(0);
        cbModeloCad.setWidth(145);
        cbModeloCad.setHeight(21);
        cbModeloCad.setLookupTable(tbProdutosModelos);
        cbModeloCad.setLookupKey("COD_MODELO");
        cbModeloCad.setLookupDesc("DESCRICAO_MODELO_INITCAP");
        cbModeloCad.setFlex(true);
        cbModeloCad.setReadOnly(true);
        cbModeloCad.setRequired(false);
        cbModeloCad.setPrompt("Modelo");
        cbModeloCad.setConstraintCheckWhen("cwImmediate");
        cbModeloCad.setConstraintCheckType("ctExpression");
        cbModeloCad.setConstraintFocusOnError(false);
        cbModeloCad.setConstraintEnableUI(true);
        cbModeloCad.setConstraintEnabled(false);
        cbModeloCad.setConstraintFormCheck(true);
        cbModeloCad.setClearOnDelKey(true);
        cbModeloCad.setUseClearButton(true);
        cbModeloCad.setHideClearButtonOnNullValue(true);
        cbModeloCad.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbModeloCadChange(event);
            processarFlow("FrmServicos", "cbModeloCad", "OnChange");
        });
        cbModeloCad.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbModeloCadClearClick(event);
            processarFlow("FrmServicos", "cbModeloCad", "OnClearClick");
        });
        FHBox28.addChildren(cbModeloCad);
        cbModeloCad.applyProperties();
        addValidatable(cbModeloCad);
    }

    public TFGrid FGridModelos = new TFGrid();

    private void init_FGridModelos() {
        FGridModelos.setName("FGridModelos");
        FGridModelos.setLeft(0);
        FGridModelos.setTop(86);
        FGridModelos.setWidth(570);
        FGridModelos.setHeight(151);
        FGridModelos.setAlign("alTop");
        FGridModelos.setTable(tbTemposPadroes);
        FGridModelos.setFlexVflex("ftTrue");
        FGridModelos.setFlexHflex("ftTrue");
        FGridModelos.setPagingEnabled(true);
        FGridModelos.setFrozenColumns(0);
        FGridModelos.setShowFooter(false);
        FGridModelos.setShowHeader(true);
        FGridModelos.setMultiSelection(false);
        FGridModelos.setGroupingEnabled(false);
        FGridModelos.setGroupingExpanded(false);
        FGridModelos.setGroupingShowFooter(false);
        FGridModelos.setCrosstabEnabled(false);
        FGridModelos.setCrosstabGroupType("cgtConcat");
        FGridModelos.setEditionEnabled(false);
        FGridModelos.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("MODELO");
        item0.setTitleCaption("Modelo");
        item0.setWidth(209);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        FGridModelos.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("TEMPO_PADRAO");
        item1.setTitleCaption("Tmo");
        item1.setWidth(60);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taRight");
        item1.setFieldType("ftDecimal");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFDecimal");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        FGridModelos.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("TEMPO_AGENDA");
        item2.setTitleCaption("Agenda");
        item2.setWidth(91);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taRight");
        item2.setFieldType("ftDecimal");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFDecimal");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        FGridModelos.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("DUA_TEC");
        item3.setTitleCaption("Dua Tec");
        item3.setWidth(81);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taRight");
        item3.setFieldType("ftDecimal");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFDecimal");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        FGridModelos.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("ATIVO2");
        item4.setTitleCaption("Ativo");
        item4.setWidth(49);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        FGridModelos.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        TFImageExpression item6 = new TFImageExpression();
        item6.setExpression("*");
        item6.setEvalType("etExpression");
        item6.setImageId(8);
        item6.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FGridModelosExcluirModeloClick(event);
            processarFlow("FrmServicos", "item6", "OnClick");
        });
        item5.getImages().add(item6);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        FGridModelos.getColumns().add(item5);
        FVBox46.addChildren(FGridModelos);
        FGridModelos.applyProperties();
    }

    public TFHBox FHBox29 = new TFHBox();

    private void init_FHBox29() {
        FHBox29.setName("FHBox29");
        FHBox29.setLeft(0);
        FHBox29.setTop(238);
        FHBox29.setWidth(345);
        FHBox29.setHeight(41);
        FHBox29.setBorderStyle("stNone");
        FHBox29.setPaddingTop(0);
        FHBox29.setPaddingLeft(0);
        FHBox29.setPaddingRight(0);
        FHBox29.setPaddingBottom(0);
        FHBox29.setMarginTop(0);
        FHBox29.setMarginLeft(0);
        FHBox29.setMarginRight(0);
        FHBox29.setMarginBottom(0);
        FHBox29.setSpacing(3);
        FHBox29.setFlexVflex("ftMin");
        FHBox29.setFlexHflex("ftTrue");
        FHBox29.setScrollable(false);
        FHBox29.setBoxShadowConfigHorizontalLength(10);
        FHBox29.setBoxShadowConfigVerticalLength(10);
        FHBox29.setBoxShadowConfigBlurRadius(5);
        FHBox29.setBoxShadowConfigSpreadRadius(0);
        FHBox29.setBoxShadowConfigShadowColor("clBlack");
        FHBox29.setBoxShadowConfigOpacity(75);
        FHBox29.setVAlign("tvTop");
        FVBox46.addChildren(FHBox29);
        FHBox29.applyProperties();
    }

    public TFButton btnNovoModelos = new TFButton();

    private void init_btnNovoModelos() {
        btnNovoModelos.setName("btnNovoModelos");
        btnNovoModelos.setLeft(0);
        btnNovoModelos.setTop(0);
        btnNovoModelos.setWidth(40);
        btnNovoModelos.setHeight(40);
        btnNovoModelos.setFontColor("clWindowText");
        btnNovoModelos.setFontSize(-11);
        btnNovoModelos.setFontName("Tahoma");
        btnNovoModelos.setFontStyle("[]");
        btnNovoModelos.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoModelosClick(event);
            processarFlow("FrmServicos", "btnNovoModelos", "OnClick");
        });
        btnNovoModelos.setImageId(6);
        btnNovoModelos.setColor("clBtnFace");
        btnNovoModelos.setAccess(false);
        btnNovoModelos.setIconReverseDirection(false);
        FHBox29.addChildren(btnNovoModelos);
        btnNovoModelos.applyProperties();
    }

    public TFButton btnAlterarModelo = new TFButton();

    private void init_btnAlterarModelo() {
        btnAlterarModelo.setName("btnAlterarModelo");
        btnAlterarModelo.setLeft(40);
        btnAlterarModelo.setTop(0);
        btnAlterarModelo.setWidth(40);
        btnAlterarModelo.setHeight(40);
        btnAlterarModelo.setFontColor("clWindowText");
        btnAlterarModelo.setFontSize(-11);
        btnAlterarModelo.setFontName("Tahoma");
        btnAlterarModelo.setFontStyle("[]");
        btnAlterarModelo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarModeloClick(event);
            processarFlow("FrmServicos", "btnAlterarModelo", "OnClick");
        });
        btnAlterarModelo.setImageId(7);
        btnAlterarModelo.setColor("clBtnFace");
        btnAlterarModelo.setAccess(false);
        btnAlterarModelo.setIconReverseDirection(false);
        FHBox29.addChildren(btnAlterarModelo);
        btnAlterarModelo.applyProperties();
    }

    public TFButton btnSalvarModelos = new TFButton();

    private void init_btnSalvarModelos() {
        btnSalvarModelos.setName("btnSalvarModelos");
        btnSalvarModelos.setLeft(80);
        btnSalvarModelos.setTop(0);
        btnSalvarModelos.setWidth(40);
        btnSalvarModelos.setHeight(40);
        btnSalvarModelos.setFontColor("clWindowText");
        btnSalvarModelos.setFontSize(-11);
        btnSalvarModelos.setFontName("Tahoma");
        btnSalvarModelos.setFontStyle("[]");
        btnSalvarModelos.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarModelosClick(event);
            processarFlow("FrmServicos", "btnSalvarModelos", "OnClick");
        });
        btnSalvarModelos.setImageId(4);
        btnSalvarModelos.setColor("clBtnFace");
        btnSalvarModelos.setAccess(false);
        btnSalvarModelos.setIconReverseDirection(false);
        FHBox29.addChildren(btnSalvarModelos);
        btnSalvarModelos.applyProperties();
    }

    public TFButton btnCancelarModelos = new TFButton();

    private void init_btnCancelarModelos() {
        btnCancelarModelos.setName("btnCancelarModelos");
        btnCancelarModelos.setLeft(120);
        btnCancelarModelos.setTop(0);
        btnCancelarModelos.setWidth(40);
        btnCancelarModelos.setHeight(40);
        btnCancelarModelos.setFontColor("clWindowText");
        btnCancelarModelos.setFontSize(-11);
        btnCancelarModelos.setFontName("Tahoma");
        btnCancelarModelos.setFontStyle("[]");
        btnCancelarModelos.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarModelosClick(event);
            processarFlow("FrmServicos", "btnCancelarModelos", "OnClick");
        });
        btnCancelarModelos.setImageId(9);
        btnCancelarModelos.setColor("clBtnFace");
        btnCancelarModelos.setAccess(false);
        btnCancelarModelos.setIconReverseDirection(false);
        FHBox29.addChildren(btnCancelarModelos);
        btnCancelarModelos.applyProperties();
    }

    public TFHBox FHBox30 = new TFHBox();

    private void init_FHBox30() {
        FHBox30.setName("FHBox30");
        FHBox30.setLeft(0);
        FHBox30.setTop(280);
        FHBox30.setWidth(570);
        FHBox30.setHeight(65);
        FHBox30.setBorderStyle("stNone");
        FHBox30.setPaddingTop(0);
        FHBox30.setPaddingLeft(0);
        FHBox30.setPaddingRight(0);
        FHBox30.setPaddingBottom(0);
        FHBox30.setMarginTop(0);
        FHBox30.setMarginLeft(0);
        FHBox30.setMarginRight(0);
        FHBox30.setMarginBottom(0);
        FHBox30.setSpacing(5);
        FHBox30.setFlexVflex("ftMin");
        FHBox30.setFlexHflex("ftTrue");
        FHBox30.setScrollable(false);
        FHBox30.setBoxShadowConfigHorizontalLength(10);
        FHBox30.setBoxShadowConfigVerticalLength(10);
        FHBox30.setBoxShadowConfigBlurRadius(5);
        FHBox30.setBoxShadowConfigSpreadRadius(0);
        FHBox30.setBoxShadowConfigShadowColor("clBlack");
        FHBox30.setBoxShadowConfigOpacity(75);
        FHBox30.setVAlign("tvTop");
        FVBox46.addChildren(FHBox30);
        FHBox30.applyProperties();
    }

    public TFVBox FVBox48 = new TFVBox();

    private void init_FVBox48() {
        FVBox48.setName("FVBox48");
        FVBox48.setLeft(0);
        FVBox48.setTop(0);
        FVBox48.setWidth(185);
        FVBox48.setHeight(52);
        FVBox48.setBorderStyle("stNone");
        FVBox48.setPaddingTop(0);
        FVBox48.setPaddingLeft(0);
        FVBox48.setPaddingRight(0);
        FVBox48.setPaddingBottom(0);
        FVBox48.setMarginTop(0);
        FVBox48.setMarginLeft(0);
        FVBox48.setMarginRight(0);
        FVBox48.setMarginBottom(0);
        FVBox48.setSpacing(1);
        FVBox48.setFlexVflex("ftTrue");
        FVBox48.setFlexHflex("ftTrue");
        FVBox48.setScrollable(false);
        FVBox48.setBoxShadowConfigHorizontalLength(10);
        FVBox48.setBoxShadowConfigVerticalLength(10);
        FVBox48.setBoxShadowConfigBlurRadius(5);
        FVBox48.setBoxShadowConfigSpreadRadius(0);
        FVBox48.setBoxShadowConfigShadowColor("clBlack");
        FVBox48.setBoxShadowConfigOpacity(75);
        FHBox30.addChildren(FVBox48);
        FVBox48.applyProperties();
    }

    public TFLabel FLabel29 = new TFLabel();

    private void init_FLabel29() {
        FLabel29.setName("FLabel29");
        FLabel29.setLeft(0);
        FLabel29.setTop(0);
        FLabel29.setWidth(34);
        FLabel29.setHeight(13);
        FLabel29.setCaption("Modelo");
        FLabel29.setFontColor("clWindowText");
        FLabel29.setFontSize(-11);
        FLabel29.setFontName("Tahoma");
        FLabel29.setFontStyle("[]");
        FLabel29.setVerticalAlignment("taVerticalCenter");
        FLabel29.setWordBreak(false);
        FVBox48.addChildren(FLabel29);
        FLabel29.applyProperties();
    }

    public TFString FStrModeloPesq = new TFString();

    private void init_FStrModeloPesq() {
        FStrModeloPesq.setName("FStrModeloPesq");
        FStrModeloPesq.setLeft(0);
        FStrModeloPesq.setTop(14);
        FStrModeloPesq.setWidth(169);
        FStrModeloPesq.setHeight(24);
        FStrModeloPesq.setFlex(true);
        FStrModeloPesq.setRequired(true);
        FStrModeloPesq.setConstraintCheckWhen("cwImmediate");
        FStrModeloPesq.setConstraintCheckType("ctExpression");
        FStrModeloPesq.setConstraintFocusOnError(false);
        FStrModeloPesq.setConstraintEnableUI(true);
        FStrModeloPesq.setConstraintEnabled(false);
        FStrModeloPesq.setConstraintFormCheck(true);
        FStrModeloPesq.setCharCase("ccNormal");
        FStrModeloPesq.setPwd(false);
        FStrModeloPesq.setMaxlength(0);
        FStrModeloPesq.setEnabled(false);
        FStrModeloPesq.setFontColor("clWindowText");
        FStrModeloPesq.setFontSize(-13);
        FStrModeloPesq.setFontName("Tahoma");
        FStrModeloPesq.setFontStyle("[]");
        FStrModeloPesq.setSaveLiteralCharacter(false);
        FStrModeloPesq.applyProperties();
        FVBox48.addChildren(FStrModeloPesq);
        addValidatable(FStrModeloPesq);
    }

    public TFVBox FVBox49 = new TFVBox();

    private void init_FVBox49() {
        FVBox49.setName("FVBox49");
        FVBox49.setLeft(185);
        FVBox49.setTop(0);
        FVBox49.setWidth(85);
        FVBox49.setHeight(52);
        FVBox49.setBorderStyle("stNone");
        FVBox49.setPaddingTop(20);
        FVBox49.setPaddingLeft(0);
        FVBox49.setPaddingRight(0);
        FVBox49.setPaddingBottom(0);
        FVBox49.setMarginTop(0);
        FVBox49.setMarginLeft(0);
        FVBox49.setMarginRight(0);
        FVBox49.setMarginBottom(0);
        FVBox49.setSpacing(1);
        FVBox49.setFlexVflex("ftTrue");
        FVBox49.setFlexHflex("ftFalse");
        FVBox49.setScrollable(false);
        FVBox49.setBoxShadowConfigHorizontalLength(10);
        FVBox49.setBoxShadowConfigVerticalLength(10);
        FVBox49.setBoxShadowConfigBlurRadius(5);
        FVBox49.setBoxShadowConfigSpreadRadius(0);
        FVBox49.setBoxShadowConfigShadowColor("clBlack");
        FVBox49.setBoxShadowConfigOpacity(75);
        FHBox30.addChildren(FVBox49);
        FVBox49.applyProperties();
    }

    public TFButton btnPesqModelo = new TFButton();

    private void init_btnPesqModelo() {
        btnPesqModelo.setName("btnPesqModelo");
        btnPesqModelo.setLeft(0);
        btnPesqModelo.setTop(0);
        btnPesqModelo.setWidth(40);
        btnPesqModelo.setHeight(40);
        btnPesqModelo.setAlign("alBottom");
        btnPesqModelo.setFontColor("clWindowText");
        btnPesqModelo.setFontSize(-11);
        btnPesqModelo.setFontName("Tahoma");
        btnPesqModelo.setFontStyle("[]");
        btnPesqModelo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesqModeloClick(event);
            processarFlow("FrmServicos", "btnPesqModelo", "OnClick");
        });
        btnPesqModelo.setImageId(430070);
        btnPesqModelo.setColor("clBtnFace");
        btnPesqModelo.setAccess(false);
        btnPesqModelo.setIconReverseDirection(false);
        FVBox49.addChildren(btnPesqModelo);
        btnPesqModelo.applyProperties();
    }

    public TFInteger FIntCodMod = new TFInteger();

    private void init_FIntCodMod() {
        FIntCodMod.setName("FIntCodMod");
        FIntCodMod.setLeft(270);
        FIntCodMod.setTop(0);
        FIntCodMod.setWidth(121);
        FIntCodMod.setHeight(24);
        FIntCodMod.setTable(tbTemposPadroes);
        FIntCodMod.setFieldName("COD_MODELO");
        FIntCodMod.setFlex(false);
        FIntCodMod.setRequired(false);
        FIntCodMod.setConstraintCheckWhen("cwImmediate");
        FIntCodMod.setConstraintCheckType("ctExpression");
        FIntCodMod.setConstraintFocusOnError(false);
        FIntCodMod.setConstraintEnableUI(true);
        FIntCodMod.setConstraintEnabled(false);
        FIntCodMod.setConstraintFormCheck(true);
        FIntCodMod.setMaxlength(0);
        FIntCodMod.setVisible(false);
        FIntCodMod.setFontColor("clWindowText");
        FIntCodMod.setFontSize(-13);
        FIntCodMod.setFontName("Tahoma");
        FIntCodMod.setFontStyle("[]");
        FIntCodMod.setAlignment("taRightJustify");
        FHBox30.addChildren(FIntCodMod);
        FIntCodMod.applyProperties();
        addValidatable(FIntCodMod);
    }

    public TFInteger FIntCodProduto = new TFInteger();

    private void init_FIntCodProduto() {
        FIntCodProduto.setName("FIntCodProduto");
        FIntCodProduto.setLeft(391);
        FIntCodProduto.setTop(0);
        FIntCodProduto.setWidth(121);
        FIntCodProduto.setHeight(24);
        FIntCodProduto.setTable(tbTemposPadroes);
        FIntCodProduto.setFieldName("COD_PRODUTO");
        FIntCodProduto.setFlex(false);
        FIntCodProduto.setRequired(false);
        FIntCodProduto.setConstraintCheckWhen("cwImmediate");
        FIntCodProduto.setConstraintCheckType("ctExpression");
        FIntCodProduto.setConstraintFocusOnError(false);
        FIntCodProduto.setConstraintEnableUI(true);
        FIntCodProduto.setConstraintEnabled(false);
        FIntCodProduto.setConstraintFormCheck(true);
        FIntCodProduto.setMaxlength(0);
        FIntCodProduto.setVisible(false);
        FIntCodProduto.setFontColor("clWindowText");
        FIntCodProduto.setFontSize(-13);
        FIntCodProduto.setFontName("Tahoma");
        FIntCodProduto.setFontStyle("[]");
        FIntCodProduto.setAlignment("taRightJustify");
        FHBox30.addChildren(FIntCodProduto);
        FIntCodProduto.applyProperties();
        addValidatable(FIntCodProduto);
    }

    public TFHBox FHBox31 = new TFHBox();

    private void init_FHBox31() {
        FHBox31.setName("FHBox31");
        FHBox31.setLeft(0);
        FHBox31.setTop(346);
        FHBox31.setWidth(569);
        FHBox31.setHeight(81);
        FHBox31.setBorderStyle("stNone");
        FHBox31.setPaddingTop(0);
        FHBox31.setPaddingLeft(0);
        FHBox31.setPaddingRight(0);
        FHBox31.setPaddingBottom(0);
        FHBox31.setMarginTop(0);
        FHBox31.setMarginLeft(0);
        FHBox31.setMarginRight(0);
        FHBox31.setMarginBottom(0);
        FHBox31.setSpacing(5);
        FHBox31.setFlexVflex("ftFalse");
        FHBox31.setFlexHflex("ftTrue");
        FHBox31.setScrollable(false);
        FHBox31.setBoxShadowConfigHorizontalLength(10);
        FHBox31.setBoxShadowConfigVerticalLength(10);
        FHBox31.setBoxShadowConfigBlurRadius(5);
        FHBox31.setBoxShadowConfigSpreadRadius(0);
        FHBox31.setBoxShadowConfigShadowColor("clBlack");
        FHBox31.setBoxShadowConfigOpacity(75);
        FHBox31.setVAlign("tvTop");
        FVBox46.addChildren(FHBox31);
        FHBox31.applyProperties();
    }

    public TFVBox FVBox50 = new TFVBox();

    private void init_FVBox50() {
        FVBox50.setName("FVBox50");
        FVBox50.setLeft(0);
        FVBox50.setTop(0);
        FVBox50.setWidth(154);
        FVBox50.setHeight(65);
        FVBox50.setBorderStyle("stNone");
        FVBox50.setPaddingTop(0);
        FVBox50.setPaddingLeft(0);
        FVBox50.setPaddingRight(0);
        FVBox50.setPaddingBottom(0);
        FVBox50.setMarginTop(0);
        FVBox50.setMarginLeft(0);
        FVBox50.setMarginRight(0);
        FVBox50.setMarginBottom(0);
        FVBox50.setSpacing(1);
        FVBox50.setFlexVflex("ftMin");
        FVBox50.setFlexHflex("ftTrue");
        FVBox50.setScrollable(false);
        FVBox50.setBoxShadowConfigHorizontalLength(10);
        FVBox50.setBoxShadowConfigVerticalLength(10);
        FVBox50.setBoxShadowConfigBlurRadius(5);
        FVBox50.setBoxShadowConfigSpreadRadius(0);
        FVBox50.setBoxShadowConfigShadowColor("clBlack");
        FVBox50.setBoxShadowConfigOpacity(75);
        FHBox31.addChildren(FVBox50);
        FVBox50.applyProperties();
    }

    public TFLabel FLabel30 = new TFLabel();

    private void init_FLabel30() {
        FLabel30.setName("FLabel30");
        FLabel30.setLeft(0);
        FLabel30.setTop(0);
        FLabel30.setWidth(59);
        FLabel30.setHeight(13);
        FLabel30.setCaption("TMO Padr\u00E3o");
        FLabel30.setFontColor("clWindowText");
        FLabel30.setFontSize(-11);
        FLabel30.setFontName("Tahoma");
        FLabel30.setFontStyle("[]");
        FLabel30.setVerticalAlignment("taVerticalCenter");
        FLabel30.setWordBreak(false);
        FVBox50.addChildren(FLabel30);
        FLabel30.applyProperties();
    }

    public TFDecimal FDecTMOPadraoMod = new TFDecimal();

    private void init_FDecTMOPadraoMod() {
        FDecTMOPadraoMod.setName("FDecTMOPadraoMod");
        FDecTMOPadraoMod.setLeft(0);
        FDecTMOPadraoMod.setTop(14);
        FDecTMOPadraoMod.setWidth(121);
        FDecTMOPadraoMod.setHeight(24);
        FDecTMOPadraoMod.setTable(tbTemposPadroes);
        FDecTMOPadraoMod.setFieldName("TEMPO_PADRAO");
        FDecTMOPadraoMod.setFlex(true);
        FDecTMOPadraoMod.setRequired(true);
        FDecTMOPadraoMod.setConstraintExpression("value < 0");
        FDecTMOPadraoMod.setConstraintMessage("Valor negativo n\u00E3o permitido");
        FDecTMOPadraoMod.setConstraintCheckWhen("cwImmediate");
        FDecTMOPadraoMod.setConstraintCheckType("ctExpression");
        FDecTMOPadraoMod.setConstraintFocusOnError(false);
        FDecTMOPadraoMod.setConstraintEnableUI(true);
        FDecTMOPadraoMod.setConstraintEnabled(true);
        FDecTMOPadraoMod.setConstraintFormCheck(true);
        FDecTMOPadraoMod.setMaxlength(0);
        FDecTMOPadraoMod.setPrecision(5);
        FDecTMOPadraoMod.setFontColor("clWindowText");
        FDecTMOPadraoMod.setFontSize(-13);
        FDecTMOPadraoMod.setFontName("Tahoma");
        FDecTMOPadraoMod.setFontStyle("[]");
        FDecTMOPadraoMod.setAlignment("taRightJustify");
        FVBox50.addChildren(FDecTMOPadraoMod);
        FDecTMOPadraoMod.applyProperties();
        addValidatable(FDecTMOPadraoMod);
    }

    public TFVBox FVBox51 = new TFVBox();

    private void init_FVBox51() {
        FVBox51.setName("FVBox51");
        FVBox51.setLeft(154);
        FVBox51.setTop(0);
        FVBox51.setWidth(154);
        FVBox51.setHeight(65);
        FVBox51.setBorderStyle("stNone");
        FVBox51.setPaddingTop(0);
        FVBox51.setPaddingLeft(0);
        FVBox51.setPaddingRight(0);
        FVBox51.setPaddingBottom(0);
        FVBox51.setMarginTop(0);
        FVBox51.setMarginLeft(0);
        FVBox51.setMarginRight(0);
        FVBox51.setMarginBottom(0);
        FVBox51.setSpacing(1);
        FVBox51.setFlexVflex("ftMin");
        FVBox51.setFlexHflex("ftTrue");
        FVBox51.setScrollable(false);
        FVBox51.setBoxShadowConfigHorizontalLength(10);
        FVBox51.setBoxShadowConfigVerticalLength(10);
        FVBox51.setBoxShadowConfigBlurRadius(5);
        FVBox51.setBoxShadowConfigSpreadRadius(0);
        FVBox51.setBoxShadowConfigShadowColor("clBlack");
        FVBox51.setBoxShadowConfigOpacity(75);
        FHBox31.addChildren(FVBox51);
        FVBox51.applyProperties();
    }

    public TFLabel FLabel31 = new TFLabel();

    private void init_FLabel31() {
        FLabel31.setName("FLabel31");
        FLabel31.setLeft(0);
        FLabel31.setTop(0);
        FLabel31.setWidth(61);
        FLabel31.setHeight(13);
        FLabel31.setCaption("TMO agenda");
        FLabel31.setFontColor("clWindowText");
        FLabel31.setFontSize(-11);
        FLabel31.setFontName("Tahoma");
        FLabel31.setFontStyle("[]");
        FLabel31.setVerticalAlignment("taVerticalCenter");
        FLabel31.setWordBreak(false);
        FVBox51.addChildren(FLabel31);
        FLabel31.applyProperties();
    }

    public TFDecimal FDecAgendaModelos = new TFDecimal();

    private void init_FDecAgendaModelos() {
        FDecAgendaModelos.setName("FDecAgendaModelos");
        FDecAgendaModelos.setLeft(0);
        FDecAgendaModelos.setTop(14);
        FDecAgendaModelos.setWidth(121);
        FDecAgendaModelos.setHeight(24);
        FDecAgendaModelos.setTable(tbTemposPadroes);
        FDecAgendaModelos.setFieldName("TEMPO_AGENDA");
        FDecAgendaModelos.setFlex(true);
        FDecAgendaModelos.setRequired(false);
        FDecAgendaModelos.setConstraintExpression("value < 0");
        FDecAgendaModelos.setConstraintMessage("Valor negativo n\u00E3o permitido");
        FDecAgendaModelos.setConstraintCheckWhen("cwImmediate");
        FDecAgendaModelos.setConstraintCheckType("ctExpression");
        FDecAgendaModelos.setConstraintFocusOnError(false);
        FDecAgendaModelos.setConstraintEnableUI(true);
        FDecAgendaModelos.setConstraintEnabled(true);
        FDecAgendaModelos.setConstraintFormCheck(true);
        FDecAgendaModelos.setMaxlength(0);
        FDecAgendaModelos.setPrecision(2);
        FDecAgendaModelos.setFontColor("clWindowText");
        FDecAgendaModelos.setFontSize(-13);
        FDecAgendaModelos.setFontName("Tahoma");
        FDecAgendaModelos.setFontStyle("[]");
        FDecAgendaModelos.setAlignment("taRightJustify");
        FVBox51.addChildren(FDecAgendaModelos);
        FDecAgendaModelos.applyProperties();
        addValidatable(FDecAgendaModelos);
    }

    public TFVBox FVBox52 = new TFVBox();

    private void init_FVBox52() {
        FVBox52.setName("FVBox52");
        FVBox52.setLeft(308);
        FVBox52.setTop(0);
        FVBox52.setWidth(154);
        FVBox52.setHeight(65);
        FVBox52.setBorderStyle("stNone");
        FVBox52.setPaddingTop(0);
        FVBox52.setPaddingLeft(0);
        FVBox52.setPaddingRight(0);
        FVBox52.setPaddingBottom(0);
        FVBox52.setMarginTop(0);
        FVBox52.setMarginLeft(0);
        FVBox52.setMarginRight(0);
        FVBox52.setMarginBottom(0);
        FVBox52.setSpacing(1);
        FVBox52.setFlexVflex("ftMin");
        FVBox52.setFlexHflex("ftTrue");
        FVBox52.setScrollable(false);
        FVBox52.setBoxShadowConfigHorizontalLength(10);
        FVBox52.setBoxShadowConfigVerticalLength(10);
        FVBox52.setBoxShadowConfigBlurRadius(5);
        FVBox52.setBoxShadowConfigSpreadRadius(0);
        FVBox52.setBoxShadowConfigShadowColor("clBlack");
        FVBox52.setBoxShadowConfigOpacity(75);
        FHBox31.addChildren(FVBox52);
        FVBox52.applyProperties();
    }

    public TFLabel FLabel32 = new TFLabel();

    private void init_FLabel32() {
        FLabel32.setName("FLabel32");
        FLabel32.setLeft(0);
        FLabel32.setTop(0);
        FLabel32.setWidth(62);
        FLabel32.setHeight(13);
        FLabel32.setCaption("TMO Dua tec");
        FLabel32.setFontColor("clWindowText");
        FLabel32.setFontSize(-11);
        FLabel32.setFontName("Tahoma");
        FLabel32.setFontStyle("[]");
        FLabel32.setVerticalAlignment("taVerticalCenter");
        FLabel32.setWordBreak(false);
        FVBox52.addChildren(FLabel32);
        FLabel32.applyProperties();
    }

    public TFDecimal FDecDuoModelos = new TFDecimal();

    private void init_FDecDuoModelos() {
        FDecDuoModelos.setName("FDecDuoModelos");
        FDecDuoModelos.setLeft(0);
        FDecDuoModelos.setTop(14);
        FDecDuoModelos.setWidth(121);
        FDecDuoModelos.setHeight(24);
        FDecDuoModelos.setTable(tbTemposPadroes);
        FDecDuoModelos.setFieldName("DUA_TEC");
        FDecDuoModelos.setFlex(true);
        FDecDuoModelos.setRequired(false);
        FDecDuoModelos.setConstraintExpression("value < 0");
        FDecDuoModelos.setConstraintMessage("Valor negativo n\u00E3o permitido");
        FDecDuoModelos.setConstraintCheckWhen("cwImmediate");
        FDecDuoModelos.setConstraintCheckType("ctExpression");
        FDecDuoModelos.setConstraintFocusOnError(false);
        FDecDuoModelos.setConstraintEnableUI(true);
        FDecDuoModelos.setConstraintEnabled(true);
        FDecDuoModelos.setConstraintFormCheck(true);
        FDecDuoModelos.setMaxlength(0);
        FDecDuoModelos.setPrecision(2);
        FDecDuoModelos.setFontColor("clWindowText");
        FDecDuoModelos.setFontSize(-13);
        FDecDuoModelos.setFontName("Tahoma");
        FDecDuoModelos.setFontStyle("[]");
        FDecDuoModelos.setAlignment("taRightJustify");
        FVBox52.addChildren(FDecDuoModelos);
        FDecDuoModelos.applyProperties();
        addValidatable(FDecDuoModelos);
    }

    public TFVBox FVBox53 = new TFVBox();

    private void init_FVBox53() {
        FVBox53.setName("FVBox53");
        FVBox53.setLeft(462);
        FVBox53.setTop(0);
        FVBox53.setWidth(73);
        FVBox53.setHeight(65);
        FVBox53.setBorderStyle("stNone");
        FVBox53.setPaddingTop(20);
        FVBox53.setPaddingLeft(0);
        FVBox53.setPaddingRight(0);
        FVBox53.setPaddingBottom(0);
        FVBox53.setMarginTop(0);
        FVBox53.setMarginLeft(0);
        FVBox53.setMarginRight(0);
        FVBox53.setMarginBottom(0);
        FVBox53.setSpacing(1);
        FVBox53.setFlexVflex("ftTrue");
        FVBox53.setFlexHflex("ftMin");
        FVBox53.setScrollable(false);
        FVBox53.setBoxShadowConfigHorizontalLength(10);
        FVBox53.setBoxShadowConfigVerticalLength(10);
        FVBox53.setBoxShadowConfigBlurRadius(5);
        FVBox53.setBoxShadowConfigSpreadRadius(0);
        FVBox53.setBoxShadowConfigShadowColor("clBlack");
        FVBox53.setBoxShadowConfigOpacity(75);
        FHBox31.addChildren(FVBox53);
        FVBox53.applyProperties();
    }

    public TFCheckBox ckbAtivoModelo = new TFCheckBox();

    private void init_ckbAtivoModelo() {
        ckbAtivoModelo.setName("ckbAtivoModelo");
        ckbAtivoModelo.setLeft(0);
        ckbAtivoModelo.setTop(0);
        ckbAtivoModelo.setWidth(97);
        ckbAtivoModelo.setHeight(17);
        ckbAtivoModelo.setCaption("Ativo");
        ckbAtivoModelo.setChecked(true);
        ckbAtivoModelo.setFontColor("clWindowText");
        ckbAtivoModelo.setFontSize(-11);
        ckbAtivoModelo.setFontName("Tahoma");
        ckbAtivoModelo.setFontStyle("[]");
        ckbAtivoModelo.setTable(tbTemposPadroes);
        ckbAtivoModelo.setFieldName("ATIVO");
        ckbAtivoModelo.setVerticalAlignment("taAlignTop");
        FVBox53.addChildren(ckbAtivoModelo);
        ckbAtivoModelo.applyProperties();
    }

    public TFTabsheet FTabMarca = new TFTabsheet();

    private void init_FTabMarca() {
        FTabMarca.setName("FTabMarca");
        FTabMarca.setCaption("Marca");
        FTabMarca.setVisible(true);
        FTabMarca.setClosable(false);
        pageControlServico.addChildren(FTabMarca);
        FTabMarca.applyProperties();
    }

    public TFVBox FVBox54 = new TFVBox();

    private void init_FVBox54() {
        FVBox54.setName("FVBox54");
        FVBox54.setLeft(0);
        FVBox54.setTop(0);
        FVBox54.setWidth(546);
        FVBox54.setHeight(772);
        FVBox54.setAlign("alClient");
        FVBox54.setBorderStyle("stNone");
        FVBox54.setPaddingTop(0);
        FVBox54.setPaddingLeft(5);
        FVBox54.setPaddingRight(5);
        FVBox54.setPaddingBottom(0);
        FVBox54.setMarginTop(0);
        FVBox54.setMarginLeft(0);
        FVBox54.setMarginRight(0);
        FVBox54.setMarginBottom(0);
        FVBox54.setSpacing(5);
        FVBox54.setFlexVflex("ftTrue");
        FVBox54.setFlexHflex("ftTrue");
        FVBox54.setScrollable(true);
        FVBox54.setBoxShadowConfigHorizontalLength(10);
        FVBox54.setBoxShadowConfigVerticalLength(10);
        FVBox54.setBoxShadowConfigBlurRadius(5);
        FVBox54.setBoxShadowConfigSpreadRadius(0);
        FVBox54.setBoxShadowConfigShadowColor("clBlack");
        FVBox54.setBoxShadowConfigOpacity(75);
        FTabMarca.addChildren(FVBox54);
        FVBox54.applyProperties();
    }

    public TFVBox FVBox55 = new TFVBox();

    private void init_FVBox55() {
        FVBox55.setName("FVBox55");
        FVBox55.setLeft(0);
        FVBox55.setTop(0);
        FVBox55.setWidth(570);
        FVBox55.setHeight(117);
        FVBox55.setBorderStyle("stNone");
        FVBox55.setPaddingTop(0);
        FVBox55.setPaddingLeft(0);
        FVBox55.setPaddingRight(0);
        FVBox55.setPaddingBottom(0);
        FVBox55.setMarginTop(0);
        FVBox55.setMarginLeft(0);
        FVBox55.setMarginRight(0);
        FVBox55.setMarginBottom(0);
        FVBox55.setSpacing(15);
        FVBox55.setFlexVflex("ftMin");
        FVBox55.setFlexHflex("ftTrue");
        FVBox55.setScrollable(false);
        FVBox55.setBoxShadowConfigHorizontalLength(10);
        FVBox55.setBoxShadowConfigVerticalLength(10);
        FVBox55.setBoxShadowConfigBlurRadius(5);
        FVBox55.setBoxShadowConfigSpreadRadius(0);
        FVBox55.setBoxShadowConfigShadowColor("clBlack");
        FVBox55.setBoxShadowConfigOpacity(75);
        FVBox54.addChildren(FVBox55);
        FVBox55.applyProperties();
    }

    public TFLabel lblDescServicMarcas = new TFLabel();

    private void init_lblDescServicMarcas() {
        lblDescServicMarcas.setName("lblDescServicMarcas");
        lblDescServicMarcas.setLeft(0);
        lblDescServicMarcas.setTop(0);
        lblDescServicMarcas.setWidth(180);
        lblDescServicMarcas.setHeight(24);
        lblDescServicMarcas.setCaption("Descri\u00E7\u00E3o Servi\u00E7o....");
        lblDescServicMarcas.setFontColor("clHotLight");
        lblDescServicMarcas.setFontSize(-20);
        lblDescServicMarcas.setFontName("Tahoma");
        lblDescServicMarcas.setFontStyle("[]");
        lblDescServicMarcas.setVisible(false);
        lblDescServicMarcas.setVerticalAlignment("taVerticalCenter");
        lblDescServicMarcas.setWordBreak(false);
        FVBox55.addChildren(lblDescServicMarcas);
        lblDescServicMarcas.applyProperties();
    }

    public TFHBox FHBox32 = new TFHBox();

    private void init_FHBox32() {
        FHBox32.setName("FHBox32");
        FHBox32.setLeft(0);
        FHBox32.setTop(25);
        FHBox32.setWidth(568);
        FHBox32.setHeight(41);
        FHBox32.setBorderStyle("stNone");
        FHBox32.setPaddingTop(0);
        FHBox32.setPaddingLeft(0);
        FHBox32.setPaddingRight(0);
        FHBox32.setPaddingBottom(0);
        FHBox32.setMarginTop(0);
        FHBox32.setMarginLeft(0);
        FHBox32.setMarginRight(0);
        FHBox32.setMarginBottom(0);
        FHBox32.setSpacing(1);
        FHBox32.setFlexVflex("ftMin");
        FHBox32.setFlexHflex("ftTrue");
        FHBox32.setScrollable(false);
        FHBox32.setBoxShadowConfigHorizontalLength(10);
        FHBox32.setBoxShadowConfigVerticalLength(10);
        FHBox32.setBoxShadowConfigBlurRadius(5);
        FHBox32.setBoxShadowConfigSpreadRadius(0);
        FHBox32.setBoxShadowConfigShadowColor("clBlack");
        FHBox32.setBoxShadowConfigOpacity(75);
        FHBox32.setVAlign("tvMiddle");
        FVBox55.addChildren(FHBox32);
        FHBox32.applyProperties();
    }

    public TFLabel FLabel36 = new TFLabel();

    private void init_FLabel36() {
        FLabel36.setName("FLabel36");
        FLabel36.setLeft(0);
        FLabel36.setTop(0);
        FLabel36.setWidth(401);
        FLabel36.setHeight(19);
        FLabel36.setCaption("Se n\u00E3o houver nenhuma marca cruzada serve para todas");
        FLabel36.setColor("clBtnFace");
        FLabel36.setFontColor("clBlack");
        FLabel36.setFontSize(-16);
        FLabel36.setFontName("Tahoma");
        FLabel36.setFontStyle("[]");
        FLabel36.setVerticalAlignment("taVerticalCenter");
        FLabel36.setWordBreak(false);
        FHBox32.addChildren(FLabel36);
        FLabel36.applyProperties();
    }

    public TFHBox FHBox34 = new TFHBox();

    private void init_FHBox34() {
        FHBox34.setName("FHBox34");
        FHBox34.setLeft(401);
        FHBox34.setTop(0);
        FHBox34.setWidth(138);
        FHBox34.setHeight(35);
        FHBox34.setBorderStyle("stNone");
        FHBox34.setPaddingTop(0);
        FHBox34.setPaddingLeft(0);
        FHBox34.setPaddingRight(0);
        FHBox34.setPaddingBottom(0);
        FHBox34.setMarginTop(0);
        FHBox34.setMarginLeft(0);
        FHBox34.setMarginRight(0);
        FHBox34.setMarginBottom(0);
        FHBox34.setSpacing(1);
        FHBox34.setFlexVflex("ftFalse");
        FHBox34.setFlexHflex("ftTrue");
        FHBox34.setScrollable(false);
        FHBox34.setBoxShadowConfigHorizontalLength(10);
        FHBox34.setBoxShadowConfigVerticalLength(10);
        FHBox34.setBoxShadowConfigBlurRadius(5);
        FHBox34.setBoxShadowConfigSpreadRadius(0);
        FHBox34.setBoxShadowConfigShadowColor("clBlack");
        FHBox34.setBoxShadowConfigOpacity(75);
        FHBox34.setVAlign("tvTop");
        FHBox32.addChildren(FHBox34);
        FHBox34.applyProperties();
    }

    public TFDualList FDualListMarca = new TFDualList();

    private void init_FDualListMarca() {
        FDualListMarca.setName("FDualListMarca");
        FDualListMarca.setLeft(0);
        FDualListMarca.setTop(118);
        FDualListMarca.setWidth(544);
        FDualListMarca.setHeight(409);
        FDualListMarca.setCaption("Selecionados");
        FDualListMarca.setLookupCaption("Dispon\u00EDveis");
        FDualListMarca.setCaptionHiperLink(false);
        FDualListMarca.setLookupCaptionHiperLink(false);
        FDualListMarca.setFlexVflex("ftTrue");
        FDualListMarca.setFlexHflex("ftTrue");
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO2");
        item0.setTitleCaption("Marca");
        item0.setWidth(188);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        FDualListMarca.getLookupColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESC_CONCESSIONARIA_TIPO2");
        item1.setTitleCaption("Marca Cruzada");
        item1.setWidth(186);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        FDualListMarca.getColumns().add(item1);
        FDualListMarca.setTable(tbServicoMarca);
        FDualListMarca.setLookupTable(tbConcessionariaTipo);
        FDualListMarca.setMultiSelection(false);
        FDualListMarca.setPaging(true);
        FDualListMarca.setGroupingEnabled(false);
        FDualListMarca.setGroupingExpanded(false);
        FDualListMarca.setGroupingShowFooter(false);
        FDualListMarca.setGroupingLookupEnabled(false);
        FDualListMarca.setGroupingLookupExpanded(false);
        FDualListMarca.setGroupingLookupShowFooter(false);
        FVBox54.addChildren(FDualListMarca);
        FDualListMarca.applyProperties();
    }

    public TFSchema scServicos;

    private void init_scServicos() {
        scServicos = rn.scServicos;
        scServicos.setName("scServicos");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbServicos);
        scServicos.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbServicosPrefeitura);
        scServicos.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbServicosRedBcIss);
        scServicos.getTables().add(item2);
        TFSchemaItem item3 = new TFSchemaItem();
        item3.setTable(tbServicosAdicionais);
        scServicos.getTables().add(item3);
        TFSchemaItem item4 = new TFSchemaItem();
        item4.setTable(tbTemposPadroes);
        scServicos.getTables().add(item4);
        TFSchemaItem item5 = new TFSchemaItem();
        item5.setTable(tbServicoMarca);
        scServicos.getTables().add(item5);
        scServicos.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnPesquisaServicoClick(final Event<Object> event) {
        if (btnPesquisaServico.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisaServico");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnIndicadoresClick(final Event<Object> event) {
        if (btnIndicadores.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnIndicadores");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cbCorChipChange(final Event<Object> event);

    public abstract void cbCorChipClearClick(final Event<Object> event);

    public abstract void pageControlServicoChange(final Event<Object> event);

    public abstract void cbGrupoFiltroClearClick(final Event<Object> event);

    public abstract void FStrDescFiltroEnter(final Event<Object> event);

    public void BtnLimparFiltrosClick(final Event<Object> event) {
        if (BtnLimparFiltros.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "BtnLimparFiltros");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void vBoxTabCadastroClick(final Event<Object> event);

    public abstract void FCoGrupoCadClearClick(final Event<Object> event);

    public void btnPesqCodNBSClick(final Event<Object> event) {
        if (btnPesqCodNBS.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesqCodNBS");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void ckbTerCadCheck(final Event<Object> event);

    public void FBtnPesquisarClick(final Event<Object> event) {
        if (FBtnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "FBtnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void FHBox20Click(final Event<Object> event);

    public abstract void ckbRetemIrrfCadCheck(final Event<Object> event);

    public abstract void ckbRetemInssCadCheck(final Event<Object> event);

    public void btnInsCodPrefClick(final Event<Object> event) {
        if (btnInsCodPref.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnInsCodPref");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcCodPrefClick(final Event<Object> event) {
        if (btnExcCodPref.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcCodPref");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnInsRedBaseClick(final Event<Object> event) {
        if (btnInsRedBase.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnInsRedBase");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcRedBaseClick(final Event<Object> event) {
        if (btnExcRedBase.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcRedBase");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void FDecAliqRedExit(final Event<Object> event);

    public abstract void FGridAdicionaisExcluirAdClick(final Event<Object> event);

    public void btnNovoAdClick(final Event<Object> event) {
        if (btnNovoAd.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovoAd");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarAdClick(final Event<Object> event) {
        if (btnAlterarrAd.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarrAd");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarAdClick(final Event<Object> event) {
        if (btnSalvarAd.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarAd");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelAdClick(final Event<Object> event) {
        if (btnCancelAd.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelAd");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cbMarcaCadChange(final Event<Object> event);

    public abstract void cbMarcaCadClearClick(final Event<Object> event);

    public abstract void cbFamiliaCadChange(final Event<Object> event);

    public abstract void cbFamiliaCadClearClick(final Event<Object> event);

    public abstract void cbModeloCadChange(final Event<Object> event);

    public abstract void cbModeloCadClearClick(final Event<Object> event);

    public abstract void FGridModelosExcluirModeloClick(final Event<Object> event);

    public void btnNovoModelosClick(final Event<Object> event) {
        if (btnNovoModelos.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovoModelos");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarModeloClick(final Event<Object> event) {
        if (btnAlterarModelo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarModelo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarModelosClick(final Event<Object> event) {
        if (btnSalvarModelos.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarModelos");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarModelosClick(final Event<Object> event) {
        if (btnCancelarModelos.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelarModelos");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPesqModeloClick(final Event<Object> event) {
        if (btnPesqModelo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesqModelo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void miExportarExcelServicosClick(final Event<Object> event);

    public abstract void miGridEmpresasImpostoClick(final Event<Object> event);

    public abstract void miGridServicosPrefeituraClick(final Event<Object> event);

    public abstract void miGridServicosRedBcIssClick(final Event<Object> event);

    public abstract void miGridModelosClick(final Event<Object> event);

    public abstract void tbConsultaServicoAfterScroll(final Event<Object> event);

    public abstract void tbConcessionariaTipoBeforePost(final Event<Object> event);

    public abstract void tbTemposPadroesAfterScroll(final Event<Object> event);

    public abstract void tbServicoMarcaBeforePost(final Event<Object> event);

}