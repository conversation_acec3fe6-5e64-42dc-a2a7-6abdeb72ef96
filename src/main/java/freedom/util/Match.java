/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

/**
 *
 * <AUTHOR>
 */
public class Match {

    /**
     * Função arredondar valor para duas casas decimas
     *
     * @param value informe o valor a ser arredondado
     * @return valor de retorno arredondado
     */
    public static Double arredondar(Double value) {
        double aux = (double) Math.round((value) * 100d) / 100d;
        return aux;
    }

}
