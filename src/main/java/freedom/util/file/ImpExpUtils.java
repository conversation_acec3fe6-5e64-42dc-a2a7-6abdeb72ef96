package freedom.util.file;

import java.text.ParseException;
import javax.swing.text.MaskFormatter;

/**
 *
 * <AUTHOR>
 */
public class ImpExpUtils {

    /**
     * @param valueToPad Informe o valor de preenchimento a esquerda.
     * @param filler Informe o caracter que irá preencher os valores a esquerda.
     * @param size Informe o tamanho que será preenchido os valores a esquerda.
     * @return
     */
    public static String lpad(String valueToPad, String filler, int size) {
        while (valueToPad.length() < size) {
            valueToPad = filler + valueToPad;
        }
        return valueToPad;
    }

    /**
     * @param valueToPad Informe o valor de preenchimento a direita.
     * @param filler Informe o caracter que irá preencher os valores a direita.
     * @param size Informe o tamanho que será preenchido os valores a direita.
     * @return
     */
    public static String rpad(String valueToPad, String filler, int size) {
        while (valueToPad.length() < size) {
            valueToPad = valueToPad + filler;
        }
        return valueToPad;
    }

    /**
     * @param texto Informe o valor que irá formatar a mascara.
     * @param mascara Informe a mascara que irá formartar o texto. Exemplo:
     * "###.###.###-##"
     * @return
     * @throws java.text.ParseException
     */
    public static String formatarString(String texto, String mascara)
            throws ParseException {
        MaskFormatter mf = new MaskFormatter(mascara);
        mf.setValueContainsLiteralCharacters(false);
        return mf.valueToString(texto);
    }
}
